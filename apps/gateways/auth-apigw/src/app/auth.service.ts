import { Injectable, Logger, OnModuleInit } from '@nestjs/common';
import {
  Client,
  ClientGrpc,
  RpcException,
  Transport,
} from '@nestjs/microservices';
import { join } from 'path';
import {
  firstValueFrom,
  Observable,
  map,
  tap,
  catchError,
  throwError,
} from 'rxjs';
import Long from 'long';
import {
  AssignDepartmentRequest,
  AssignDepartmentResponse,
  AssignPermissionRequest,
  AssignPermissionResponse,
  AssignRoleRequest,
  AssignRoleResponse,
  AuthService as AuthGrpcService,
  BulkCreateModulesRequest,
  BulkCreateModulesResponse,
  CreateDepartmentRequest,
  CreateDepartmentResponse,
  CreateModuleRequest,
  CreateModuleResponse,
  CreateRoleRequest,
  CreateRoleWithDetailsRequest,
  CreateRoleWithDetailsResponse,
  CreateUserRequest,
  DeleteRoleRequest,
  DeleteRoleResponse,
  DeleteUserRequest,
  DeleteUserResponse,
  DepartmentInfo,
  GenerateOtpRequest,
  GenerateOtpResponse,
  GetDataRequest,
  ListDepartmentsResponse,
  ListDepartmentsWithUsersResponse,
  ListModulesResponse,
  ListRolesRequest,
  ListRolesResponse,
  ListUsersRequest,
  ListUsersResponse,
  LoginRequest,
  LoginResponse,
  LogoutRequest,
  LogoutResponse,
  RefreshTokenRequest,
  RefreshTokenResponse,
  RegisterRequest,
  RegisterResponse,
  RemovePermissionRequest,
  RemovePermissionResponse,
  RemoveRoleRequest,
  RemoveRoleResponse,
  RoleDetailsResponse,
  RoleResponse,
  RoleWithDetailsResponse,
  SsoAuthRequest,
  SsoAuthResponse,
  UpdateRoleRequest,
  UpdateRoleResponse,
  UpdateUserRequest,
  UserResponse,
  ValidateTokenRequest,
  ValidateTokenResponse,
  VerifyOtpRequest,
  VerifyOtpResponse,
} from './auth.interface';
import {
  longReplacer,
  serializeDepartmentIds,
  serializeDeptAndUserIds,
  serializeUserResponse,
  toNum,
  toNumber,
} from './utils/serializeResponse';
import { status } from '@grpc/grpc-js';

@Injectable()
export class AuthClientService implements OnModuleInit {
  @Client({
    transport: Transport.GRPC,
    options: {
      package: 'auth',
      protoPath: join(process.cwd(), 'libs/shared/dto/src/lib/auth/auth.proto'),
      url: 'auth-service:50052',
    },
  })
  private readonly client: ClientGrpc;

  private authService: AuthGrpcService;

  onModuleInit() {
    this.authService = this.client.getService<AuthGrpcService>('AuthService');
  }

  // ─── Authentication ─────────────────────────────────────────────────
  // #region
  register(req: RegisterRequest): Observable<RegisterResponse> {
    return this.authService.register(req);
  }

  async login(
    email: string,
    password: string,
    ipAddress: string,
    userAgent?: string
  ) {
    const resp = await firstValueFrom(
      this.authService.login({ email, password, ipAddress, userAgent })
    );
    // Logger.debug(
    //   `Login successful for user: ${JSON.stringify(
    //     serializeUserResponse(resp.user),
    //     null,
    //     2
    //   )}`
    // );
    // const userLoginData = { ...resp, user: resp.user };
    return { ...resp, user: serializeUserResponse(resp.user) };
  }

  logout(token: string): Observable<LogoutResponse> {
    return this.authService.logout({ accessToken: token });
  }

  async validateToken(token: string) {
    const resp = await firstValueFrom(
      this.authService.validateToken({ token })
    );
    return { ...resp, user: serializeUserResponse(resp.user) };
  }

  refreshToken(refreshToken: string): Observable<RefreshTokenResponse> {
    return this.authService.refreshToken({ refreshToken });
  }

  async ssoAuth(req: SsoAuthRequest) {
    const resp = await firstValueFrom(this.authService.ssoAuth(req));
    return { ...resp, user: serializeUserResponse(resp.user) };
  }

  // ─── OTP ─────────────────────────────────────────────────────────────

  generateOtp(
    email: string,
    type: string,
    ipAddress?: string,
    userAgent?: string
  ): Observable<GenerateOtpResponse> {
    return this.authService.generateOtp({ email, type, ipAddress, userAgent });
  }

  verifyOtp(
    email: string,
    otp: string,
    type: string,
    ipAddress?: string,
    userAgent?: string
  ): Observable<VerifyOtpResponse> {
    return this.authService.verifyOtp({
      email,
      otp,
      type,
      ipAddress,
      userAgent,
    });
  }
  // #endregion

  // ─── User Management ─────────────────────────────────────────────────
  // #region

  // #endregion

  // ─── Role Management ─────────────────────────────────────────────────
  // #region
  createRoleWithDetails(
    data: CreateRoleWithDetailsRequest
  ): Observable<CreateRoleWithDetailsResponse> {
    Logger.debug(`createRoleWithDetails: ${JSON.stringify(data, null, 2)}`);
    return this.authService.createRoleWithDetails(data).pipe(
      map((resp) => {
        // helper to convert Long to number
        return {
          success: resp.success,
          message: resp.message,
          role: {
            id: toNum(resp.role.id),
            name: resp.role.name,
          },
        };
      })
    );
  }

  getRolesWithDetails(
    getArgs: GetDataRequest
  ): Observable<RoleWithDetailsResponse> {
    return this.authService.getRolesWithDetails(getArgs).pipe(
      tap((resp) => {
        // stringify with replacer, then 2-space indent
        // const serialized = JSON.stringify(
        //   resp,
        //   (_key, value) => (Long.isLong(value) ? value.toNumber() : value),
        //   2
        // );
        // Logger.debug(serialized, 'getRolesWithDetails');
      }),
      map((resp) => {
        const roles = (resp.roles || []).map((role) => {
          const normalizeId = (id: any) =>
            Long.isLong(id) ? id.toNumber() : id;

          const departments = (role.department || []).map((dep) => ({
            ...dep,
            id: normalizeId(dep.id),
            users: (dep.users || []).map((u) => ({
              ...u,
              id: normalizeId(u.id),
            })),
          }));

          const modules = (role.modules || []).map((mod) => ({
            ...mod,
            id: normalizeId(mod.id),
            features: (mod.features || []).map((feat) => ({
              ...feat,
              id: normalizeId(feat.id),
              subFeatures: (feat.subFeatures || []).map((sf) => ({
                ...sf,
                id: normalizeId(sf.id),
              })),
            })),
          }));

          return {
            ...role,
            id: normalizeId(role.id),
            department: departments,
            modules,
          };
        });

        return {
          ...resp,
          roles,
        };
      })
    );
  }

  getRoleDetails(
    name: string,
    getArgs: GetDataRequest
  ): Observable<RoleDetailsResponse> {
    return this.authService.getRoleDetails({ name, getArgs }).pipe(
      map((resp) => ({
        success: resp.success,
        message: resp.message,
        role: {
          id: toNum(resp.role.id),
          role: resp.role.role,
          department: resp.role.department?.map((d) => ({
            id: toNum(d.id),
            name: d.name,
            users: d.users?.map((u) => ({
              id: toNum(u.id),
              name: u.name,
              email: u.email,
            })),
          })),
          modules: resp.role.modules?.map((m) => ({
            id: toNum(m.id),
            module: m.module,
            features: m.features?.map((f) => ({
              id: toNum(f.id),
              feature: f.feature,
              permissions: f.permissions,
              subFeatures: f.subFeatures?.map((sf) => ({
                id: toNum(sf.id),
                subFeature: sf.subFeature,
                permissions: sf.permissions,
              })),
            })),
          })),
        },
      })),
      catchError((err) => {
        Logger.warn(`gRPC getRoleDetails failed: [${err.code}] ${err.message}`);
        // If it’s a NOT_FOUND from the auth-service, re-throw it unchanged:
        if (err.code === status.NOT_FOUND) {
          return throwError(
            () =>
              new RpcException({
                code: status.NOT_FOUND,
                message: err.message,
                details: err.details,
              })
          );
        }
        // for anything else, bubble it up as a 500
        return throwError(() => new RpcException(err));
      })
    );
  }

  renameRole(data: UpdateRoleRequest): Observable<UpdateRoleResponse> {
    return this.authService.updateRole(data);
  }

  deleteRole(data: DeleteRoleRequest): Observable<DeleteRoleResponse> {
    return this.authService.deleteRole(data);
  }

  // #endregion

  // ─── User–Role & Permission ──────────────────────────────────────────
  // #region

  // #endregion

  // ─── Module & Feature ────────────────────────────────────────────────
  // #region
  createModule(data: CreateModuleRequest): Observable<CreateModuleResponse> {
    return this.authService.createModule(data);
  }

  /** bulk call */
  bulkCreateModules(
    data: BulkCreateModulesRequest
  ): Observable<BulkCreateModulesResponse> {
    return this.authService.bulkCreateModules(data);
  }

  listModules(getArgs: GetDataRequest): Observable<ListModulesResponse> {
    return this.authService.listModules(getArgs);
  }

  // #endregion

  // ─── Department ──────────────────────────────────────────────────────
  // #region
  createDepartment(
    data: CreateDepartmentRequest
  ): Observable<CreateDepartmentResponse> {
    // Logger.debug(`createDepartment: ${JSON.stringify(data, null, 2)}`);
    return this.authService.createDepartment(data);
  }

  assignDepartment(
    data: AssignDepartmentRequest
  ): Observable<AssignDepartmentResponse> {
    return this.authService.assignDepartment(data);
  }

  listDepartments(
    getArgs: GetDataRequest
  ): Observable<ListDepartmentsResponse> {
    return this.authService.listDepartments(getArgs).pipe(
      map((data: DepartmentInfo[] | ListDepartmentsResponse) => {
        // Check if data is DepartmentInfo[] (type mismatch case)
        if (Array.isArray(data)) {
          const departments = serializeDepartmentIds(data);
          return {
            success: true,
            message: 'Departments retrieved',
            departments,
          };
        }

        // Handle ListDepartmentsResponse
        if (!data || !data.departments) {
          return {
            success: false,
            message: 'Invalid response from auth service',
            departments: [],
          };
        }

        const serializedDepartments = serializeDepartmentIds(data.departments);
        return {
          ...data,
          departments: serializedDepartments,
        };
      })
    );
  }

  listDepartmentsWithUsers(
    getArgs: GetDataRequest
  ): Observable<ListDepartmentsWithUsersResponse> {
    return this.authService.listDepartmentsWithUsers(getArgs).pipe(
      map((resp: ListDepartmentsWithUsersResponse) => {
        // normalize every dept (and nested children + users) to real numbers
        // Logger.debug(
        //   `listDepartmentsWithUsers: ${JSON.stringify(resp, longReplacer, 2)}`
        // );
        const departments = (resp.departments || []).map(
          serializeDeptAndUserIds
        );
        return {
          success: resp.success,
          message: resp.message,
          departments,
        };
      })
    );
  }
  // #endregion
}
