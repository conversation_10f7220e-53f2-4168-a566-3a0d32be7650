[{"module": "Dashboard Access", "features": [{"feature": "Overview of key metrics", "roles": {"Super Admin": true, "Regional Manager": false, "Country Director": false, "Key Accounts Manager": true, "University": false, "Student (Individual)": true, "Admin": false, "HR": false, "Accounts Manager": false, "Application Manager": false, "Application Officer": false, "Counselor": false, "Student": false}}, {"feature": "Quick links to important actions", "roles": {"Super Admin": true, "Regional Manager": false, "Country Director": false, "Key Accounts Manager": true, "University": false, "Student (Individual)": true, "Admin": false, "HR": false, "Accounts Manager": false, "Application Manager": false, "Application Officer": false, "Counselor": false, "Student": false}}, {"feature": "Notifications and reminders", "roles": {"Super Admin": true, "Regional Manager": false, "Country Director": false, "Key Accounts Manager": true, "University": false, "Student (Individual)": true, "Admin": false, "HR": false, "Accounts Manager": false, "Application Manager": false, "Application Officer": false, "Counselor": false, "Student": false}}]}, {"module": "Users Management", "features": [{"feature": "Create", "roles": {"Super Admin": true, "Regional Manager": false, "Country Director": false, "Key Accounts Manager": false, "University": true, "Student (Individual)": false, "Admin": true, "HR": false, "Accounts Manager": false, "Application Manager": false, "Application Officer": false, "Counselor": false, "Student": false}}, {"feature": "Delete", "roles": {"Super Admin": true, "Regional Manager": false, "Country Director": false, "Key Accounts Manager": false, "University": false, "Student (Individual)": false, "Admin": true, "HR": false, "Accounts Manager": false, "Application Manager": false, "Application Officer": false, "Counselor": false, "Student": false}}, {"feature": "Deactivate", "roles": {"Super Admin": true, "Regional Manager": false, "Country Director": false, "Key Accounts Manager": false, "University": false, "Student (Individual)": false, "Admin": false, "HR": true, "Accounts Manager": false, "Application Manager": true, "Application Officer": false, "Counselor": false, "Student": false}}, {"feature": "View", "roles": {"Super Admin": true, "Regional Manager": false, "Country Director": false, "Key Accounts Manager": false, "University": false, "Student (Individual)": false, "Admin": false, "HR": true, "Accounts Manager": false, "Application Manager": true, "Application Officer": true, "Counselor": true, "Student": false}}, {"feature": "View Activity", "roles": {"Super Admin": true, "Regional Manager": false, "Country Director": false, "Key Accounts Manager": false, "University": false, "Student (Individual)": false, "Admin": false, "HR": true, "Accounts Manager": false, "Application Manager": true, "Application Officer": true, "Counselor": true, "Student": false}}, {"feature": "Edit", "roles": {"Super Admin": true, "Regional Manager": false, "Country Director": false, "Key Accounts Manager": false, "University": false, "Student (Individual)": false, "Admin": false, "HR": false, "Accounts Manager": false, "Application Manager": false, "Application Officer": false, "Counselor": false, "Student": false}}]}, {"module": "Leads Management", "features": [{"feature": "Create", "roles": {"Super Admin": false, "Regional Manager": false, "Country Director": false, "Key Accounts Manager": false, "University": false, "Student (Individual)": false, "Admin": false, "HR": false, "Accounts Manager": false, "Application Manager": false, "Application Officer": false, "Counselor": false, "Student": false}}, {"feature": "Follow-up Tracking", "roles": {"Super Admin": false, "Regional Manager": false, "Country Director": false, "Key Accounts Manager": false, "University": false, "Student (Individual)": false, "Admin": false, "HR": false, "Accounts Manager": false, "Application Manager": false, "Application Officer": false, "Counselor": false, "Student": false}}, {"feature": "Assign leades to user", "roles": {"Super Admin": false, "Regional Manager": false, "Country Director": false, "Key Accounts Manager": false, "University": false, "Student (Individual)": false, "Admin": false, "HR": false, "Accounts Manager": false, "Application Manager": false, "Application Officer": false, "Counselor": false, "Student": false}}, {"feature": "lead Conversion to application", "roles": {"Super Admin": false, "Regional Manager": false, "Country Director": false, "Key Accounts Manager": false, "University": false, "Student (Individual)": false, "Admin": false, "HR": false, "Accounts Manager": false, "Application Manager": false, "Application Officer": false, "Counselor": false, "Student": false}}]}, {"module": "Application Management", "features": [{"feature": "Track application status", "roles": {"Super Admin": true, "Regional Manager": true, "Country Director": true, "Key Accounts Manager": true, "University": true, "Student (Individual)": true, "Admin": true, "HR": false, "Accounts Manager": false, "Application Manager": true, "Application Officer": true, "Counselor": true, "Student": true}}, {"feature": "Create", "roles": {"Super Admin": true, "Regional Manager": false, "Country Director": false, "Key Accounts Manager": true, "University": false, "Student (Individual)": true, "Admin": true, "HR": false, "Accounts Manager": false, "Application Manager": true, "Application Officer": true, "Counselor": true, "Student": false}}, {"feature": "Delete", "roles": {"Super Admin": true, "Regional Manager": false, "Country Director": false, "Key Accounts Manager": false, "University": false, "Student (Individual)": false, "Admin": false, "HR": false, "Accounts Manager": false, "Application Manager": false, "Application Officer": false, "Counselor": false, "Student": false}}, {"feature": "View", "roles": {"Super Admin": true, "Regional Manager": false, "Country Director": false, "Key Accounts Manager": true, "University": true, "Student (Individual)": true, "Admin": true, "HR": false, "Accounts Manager": false, "Application Manager": true, "Application Officer": true, "Counselor": true, "Student": true}}, {"feature": "Edit", "roles": {"Super Admin": true, "Regional Manager": false, "Country Director": false, "Key Accounts Manager": true, "University": false, "Student (Individual)": false, "Admin": false, "HR": false, "Accounts Manager": false, "Application Manager": false, "Application Officer": false, "Counselor": false, "Student": false}}]}, {"module": "Financial Transaction", "features": [{"feature": "Create", "roles": {"Super Admin": false, "Regional Manager": false, "Country Director": false, "Key Accounts Manager": false, "University": false, "Student (Individual)": false, "Admin": false, "HR": false, "Accounts Manager": false, "Application Manager": false, "Application Officer": false, "Counselor": false, "Student": false}}, {"feature": "Delete", "roles": {"Super Admin": false, "Regional Manager": false, "Country Director": false, "Key Accounts Manager": false, "University": false, "Student (Individual)": false, "Admin": false, "HR": false, "Accounts Manager": false, "Application Manager": false, "Application Officer": false, "Counselor": false, "Student": false}}, {"feature": "View", "roles": {"Super Admin": false, "Regional Manager": false, "Country Director": false, "Key Accounts Manager": false, "University": false, "Student (Individual)": false, "Admin": false, "HR": false, "Accounts Manager": false, "Application Manager": false, "Application Officer": false, "Counselor": false, "Student": false}}, {"feature": "Track payments", "roles": {"Super Admin": false, "Regional Manager": false, "Country Director": false, "Key Accounts Manager": false, "University": false, "Student (Individual)": false, "Admin": false, "HR": false, "Accounts Manager": false, "Application Manager": false, "Application Officer": false, "Counselor": false, "Student": false}}, {"feature": "Edit", "roles": {"Super Admin": false, "Regional Manager": false, "Country Director": false, "Key Accounts Manager": false, "University": false, "Student (Individual)": false, "Admin": false, "HR": false, "Accounts Manager": false, "Application Manager": false, "Application Officer": false, "Counselor": false, "Student": false}}]}, {"module": "HR Module Access", "features": [{"feature": "Employee records", "roles": {"Super Admin": true, "Regional Manager": false, "Country Director": false, "Key Accounts Manager": false, "University": false, "Student (Individual)": false, "Admin": true, "HR": true, "Accounts Manager": false, "Application Manager": false, "Application Officer": false, "Counselor": false, "Student": false}}, {"feature": "Attendance tracking", "roles": {"Super Admin": true, "Regional Manager": false, "Country Director": false, "Key Accounts Manager": false, "University": false, "Student (Individual)": false, "Admin": true, "HR": true, "Accounts Manager": false, "Application Manager": false, "Application Officer": false, "Counselor": false, "Student": false}}, {"feature": "Leave management", "roles": {"Super Admin": true, "Regional Manager": false, "Country Director": false, "Key Accounts Manager": false, "University": false, "Student (Individual)": false, "Admin": true, "HR": true, "Accounts Manager": false, "Application Manager": false, "Application Officer": false, "Counselor": false, "Student": false}}, {"feature": "Role assignments", "roles": {"Super Admin": true, "Regional Manager": false, "Country Director": false, "Key Accounts Manager": false, "University": false, "Student (Individual)": false, "Admin": true, "HR": true, "Accounts Manager": false, "Application Manager": false, "Application Officer": false, "Counselor": false, "Student": false}}]}, {"module": "System Settings", "features": [{"feature": "CRM configuration", "roles": {"Super Admin": true, "Regional Manager": false, "Country Director": false, "Key Accounts Manager": false, "University": false, "Student (Individual)": false, "Admin": false, "HR": false, "Accounts Manager": false, "Application Manager": false, "Application Officer": false, "Counselor": false, "Student": false}}, {"feature": "Role and permission setup", "roles": {"Super Admin": true, "Regional Manager": false, "Country Director": false, "Key Accounts Manager": false, "University": false, "Student (Individual)": false, "Admin": true, "HR": false, "Accounts Manager": false, "Application Manager": false, "Application Officer": false, "Counselor": false, "Student": false}}, {"feature": "Notification preferences", "roles": {"Super Admin": true, "Regional Manager": false, "Country Director": false, "Key Accounts Manager": false, "University": false, "Student (Individual)": false, "Admin": false, "HR": false, "Accounts Manager": false, "Application Manager": false, "Application Officer": false, "Counselor": false, "Student": false}}, {"feature": "Backup and security settings", "roles": {"Super Admin": true, "Regional Manager": false, "Country Director": false, "Key Accounts Manager": false, "University": false, "Student (Individual)": false, "Admin": false, "HR": false, "Accounts Manager": false, "Application Manager": false, "Application Officer": false, "Counselor": false, "Student": false}}]}, {"module": "Reports & Analytics", "features": [{"feature": "Generate reports by module (Leads, Applications, Finance, etc.)", "roles": {"Super Admin": true, "Regional Manager": false, "Country Director": false, "Key Accounts Manager": false, "University": false, "Student (Individual)": false, "Admin": false, "HR": false, "Accounts Manager": false, "Application Manager": false, "Application Officer": false, "Counselor": false, "Student": false}}, {"feature": "Export to PDF/Excel", "roles": {"Super Admin": true, "Regional Manager": false, "Country Director": false, "Key Accounts Manager": false, "University": false, "Student (Individual)": true, "Admin": true, "HR": true, "Accounts Manager": true, "Application Manager": true, "Application Officer": true, "Counselor": true, "Student": true}}, {"feature": "Dashboard widgets with filters", "roles": {"Super Admin": true, "Regional Manager": false, "Country Director": false, "Key Accounts Manager": false, "University": false, "Student (Individual)": true, "Admin": true, "HR": true, "Accounts Manager": true, "Application Manager": true, "Application Officer": true, "Counselor": true, "Student": true}}]}, {"module": "Agency Management", "features": [{"feature": "Onboard new agency partners", "roles": {"Super Admin": true, "Regional Manager": false, "Country Director": true, "Key Accounts Manager": false, "University": false, "Student (Individual)": false, "Admin": false, "HR": false, "Accounts Manager": false, "Application Manager": false, "Application Officer": false, "Counselor": false, "Student": false}}, {"feature": "Track agency performance", "roles": {"Super Admin": true, "Regional Manager": false, "Country Director": false, "Key Accounts Manager": true, "University": false, "Student (Individual)": false, "Admin": true, "HR": false, "Accounts Manager": false, "Application Manager": false, "Application Officer": false, "Counselor": false, "Student": false}}, {"feature": "View applications submitted by agencies", "roles": {"Super Admin": true, "Regional Manager": false, "Country Director": false, "Key Accounts Manager": true, "University": false, "Student (Individual)": false, "Admin": true, "HR": false, "Accounts Manager": false, "Application Manager": false, "Application Officer": false, "Counselor": false, "Student": false}}, {"feature": "Set commission rates", "roles": {"Super Admin": true, "Regional Manager": false, "Country Director": false, "Key Accounts Manager": false, "University": false, "Student (Individual)": false, "Admin": false, "HR": false, "Accounts Manager": false, "Application Manager": false, "Application Officer": false, "Counselor": false, "Student": false}}]}, {"module": "University Management", "features": [{"feature": "Add/update university profiles", "roles": {"Super Admin": true, "Regional Manager": false, "Country Director": false, "Key Accounts Manager": false, "University": false, "Student (Individual)": false, "Admin": false, "HR": false, "Accounts Manager": false, "Application Manager": false, "Application Officer": false, "Counselor": false, "Student": false}}, {"feature": "Manage university requirements", "roles": {"Super Admin": true, "Regional Manager": false, "Country Director": false, "Key Accounts Manager": false, "University": false, "Student (Individual)": false, "Admin": false, "HR": false, "Accounts Manager": false, "Application Manager": false, "Application Officer": false, "Counselor": false, "Student": false}}, {"feature": "Application intake calendars", "roles": {"Super Admin": true, "Regional Manager": false, "Country Director": false, "Key Accounts Manager": false, "University": true, "Student (Individual)": false, "Admin": false, "HR": false, "Accounts Manager": false, "Application Manager": false, "Application Officer": false, "Counselor": false, "Student": false}}, {"feature": "Course list and program info", "roles": {"Super Admin": true, "Regional Manager": false, "Country Director": false, "Key Accounts Manager": false, "University": true, "Student (Individual)": false, "Admin": false, "HR": false, "Accounts Manager": false, "Application Manager": false, "Application Officer": false, "Counselor": false, "Student": false}}, {"feature": "Request University for Onboarding", "roles": {"Super Admin": false, "Regional Manager": false, "Country Director": false, "Key Accounts Manager": true, "University": false, "Student (Individual)": false, "Admin": false, "HR": false, "Accounts Manager": false, "Application Manager": false, "Application Officer": false, "Counselor": false, "Student": false}}]}, {"module": "Student Profile Management", "features": [{"feature": "Communication logs", "roles": {"Super Admin": true, "Regional Manager": false, "Country Director": false, "Key Accounts Manager": false, "University": false, "Student (Individual)": true, "Admin": false, "HR": false, "Accounts Manager": false, "Application Manager": false, "Application Officer": false, "Counselor": false, "Student": false}}, {"feature": "Create", "roles": {"Super Admin": true, "Regional Manager": false, "Country Director": false, "Key Accounts Manager": false, "University": false, "Student (Individual)": true, "Admin": false, "HR": false, "Accounts Manager": false, "Application Manager": true, "Application Officer": true, "Counselor": true, "Student": false}}, {"feature": "Delete", "roles": {"Super Admin": true, "Regional Manager": false, "Country Director": false, "Key Accounts Manager": false, "University": false, "Student (Individual)": false, "Admin": false, "HR": false, "Accounts Manager": false, "Application Manager": false, "Application Officer": false, "Counselor": false, "Student": false}}, {"feature": "View", "roles": {"Super Admin": true, "Regional Manager": false, "Country Director": false, "Key Accounts Manager": false, "University": true, "Student (Individual)": true, "Admin": false, "HR": false, "Accounts Manager": false, "Application Manager": true, "Application Officer": true, "Counselor": true, "Student": true}}, {"feature": "Edit", "roles": {"Super Admin": true, "Regional Manager": false, "Country Director": false, "Key Accounts Manager": false, "University": false, "Student (Individual)": true, "Admin": false, "HR": false, "Accounts Manager": false, "Application Manager": true, "Application Officer": true, "Counselor": true, "Student": false}}]}, {"module": "Support & Maintenance / IT/ System Admins", "features": [{"feature": "Submit support tickets", "roles": {"Super Admin": true, "Regional Manager": false, "Country Director": false, "Key Accounts Manager": false, "University": false, "Student (Individual)": false, "Admin": false, "HR": false, "Accounts Manager": false, "Application Manager": false, "Application Officer": false, "Counselor": false, "Student": false}}, {"feature": "IT activity log", "roles": {"Super Admin": true, "Regional Manager": false, "Country Director": false, "Key Accounts Manager": false, "University": false, "Student (Individual)": false, "Admin": false, "HR": false, "Accounts Manager": false, "Application Manager": false, "Application Officer": false, "Counselor": false, "Student": false}}, {"feature": "System status checks", "roles": {"Super Admin": true, "Regional Manager": false, "Country Director": false, "Key Accounts Manager": false, "University": false, "Student (Individual)": false, "Admin": false, "HR": false, "Accounts Manager": false, "Application Manager": false, "Application Officer": false, "Counselor": false, "Student": false}}, {"feature": "Maintenance and Setup", "roles": {"Super Admin": true, "Regional Manager": false, "Country Director": false, "Key Accounts Manager": false, "University": false, "Student (Individual)": false, "Admin": false, "HR": false, "Accounts Manager": false, "Application Manager": false, "Application Officer": false, "Counselor": false, "Student": false}}]}, {"module": "Task Management", "features": [{"feature": "Create", "roles": {"Super Admin": false, "Regional Manager": false, "Country Director": false, "Key Accounts Manager": false, "University": false, "Student (Individual)": false, "Admin": false, "HR": false, "Accounts Manager": false, "Application Manager": false, "Application Officer": false, "Counselor": false, "Student": false}}, {"feature": "Delete", "roles": {"Super Admin": false, "Regional Manager": false, "Country Director": false, "Key Accounts Manager": false, "University": false, "Student (Individual)": false, "Admin": false, "HR": false, "Accounts Manager": false, "Application Manager": false, "Application Officer": false, "Counselor": false, "Student": false}}, {"feature": "Track task status and progress", "roles": {"Super Admin": false, "Regional Manager": false, "Country Director": false, "Key Accounts Manager": false, "University": false, "Student (Individual)": false, "Admin": false, "HR": false, "Accounts Manager": false, "Application Manager": false, "Application Officer": false, "Counselor": false, "Student": false}}, {"feature": "Edit", "roles": {"Super Admin": false, "Regional Manager": false, "Country Director": false, "Key Accounts Manager": false, "University": false, "Student (Individual)": false, "Admin": false, "HR": false, "Accounts Manager": false, "Application Manager": false, "Application Officer": false, "Counselor": false, "Student": false}}]}, {"sub": "1", "email": "<EMAIL>", "roles": ["SuperAdmin"], "departments": ["Admin"], "permissions": ["DashboardAccess:Overviewofkeymetrics", "DashboardAccess:Quicklinkstoimportantactions", "DashboardAccess:<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "TaskManagement:Create", "TaskManagement:Delete", "UsersManagement:Deactivate", "StudentProfileManagement:View", "UsersManagement:ViewActivity", "TaskManagement:Edit", "ApplicationManagement:Trackapplicationstatus", "HRfeatureAccess:Employeerecords", "HRfeatureAccess:Attendancetracking", "HRfeatureAccess:Leavemanagement", "HRfeatureAccess:Roleassignments", "SystemSettings:CRMconfiguration", "SystemSettings:Roleandpermissionsetup", "SystemSettings:Notificationpreferences", "SystemSettings:Backupandsecuritysettings", "Reports&Analytics:Generatereportsbyfeature(Leads,Applications,Finance,etc.)", "Reports&Analytics:ExporttoPDF/Excel", "Reports&Analytics:Dashboardwidgetswithfilters", "AgencyManagement:Onboardnewagencypartners", "AgencyManagement:Trackagencyperformance", "AgencyManagement:Viewapplicationssubmittedbyagencies", "AgencyManagement:Setcommissionrates", "UniversityManagement:Add/updateuniversityprofiles", "UniversityManagement:Manageuniversityrequirements", "UniversityManagement:Applicationintakecalendars", "UniversityManagement:Courselistandprograminfo", "StudentProfileManagement:Communicationlogs", "Support&Maintenance/IT/SystemAdmins:Submitsupporttickets", "Support&Maintenance/IT/SystemAdmins:ITactivitylog", "Support&Maintenance/IT/SystemAdmins:Systemstatuschecks", "Support&Maintenance/IT/SystemAdmins:MaintenanceandSetup"], "type": "access", "iat": 1750308941, "exp": 1750312541}]