import { Module } from '@nestjs/common';
import { SequelizeModule } from '@nestjs/sequelize';
import { SeedService } from './seed.service';
import { User } from '../user/model/user.model';
import { UserRole } from '../user/model/user-role.model';
import { Role } from '../user/model/role.model';
import { Feature } from '../user/model/feature.model';
import { SubFeature } from '../user/model/sub-feature.model';
import { Module as ModuleModel } from '../user/model/module.model';
import { RoleFeaturePermission } from '../user/model/role-feature-permission.model';
import { RoleSubFeaturePermission } from '../user/model/role-sub-feature-permission.model';
import { Token } from '../auth/token.model';
import { SocialSite } from '../user/model/social-site.model';
import { Department } from '../user/model/department.model';
import { UserDepartment } from '../user/model/user‐department.model';
import { DepartmentFeaturePermission } from '../user/model/department-feature-permission.model';
import { DepartmentSubFeaturePermission } from '../user/model/department-sub-feature-permission.model';
import { EmployeePersonal } from '../employee/models/employee-personal.model';
import { EmployeeAddress } from '../employee/models/employee-address.model';
import { EmployeeIdentityDoc } from '../employee/models/employee-identity-doc.model';
import { EmployeeBankAccount } from '../employee/models/employee-bank-account.model';
import { EmployeeEmergencyContact } from '../employee/models/employee-emergency-contact.model';

@Module({
  imports: [
    SequelizeModule.forFeature([
      Feature,
      Department,
      UserDepartment,
      DepartmentFeaturePermission,
      DepartmentSubFeaturePermission,
      SubFeature,
      ModuleModel, // Module
      Role,
      RoleFeaturePermission,
      RoleSubFeaturePermission,
      User,
      UserRole,
      Token,
      SocialSite,
      EmployeePersonal,
      EmployeeAddress,
      EmployeeEmergencyContact,
      EmployeeIdentityDoc,
      EmployeeBankAccount,
    ]),
  ],
  providers: [SeedService],
  exports: [SeedService],
})
export class SeedModule {}
