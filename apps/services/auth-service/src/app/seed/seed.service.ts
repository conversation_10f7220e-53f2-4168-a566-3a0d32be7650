import { Injectable, Logger } from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';
import * as bcrypt from 'bcrypt';

// Sequelize models
import { Module } from '../user/model/module.model';
import { Feature } from '../user/model/feature.model';
import { SubFeature } from '../user/model/sub-feature.model';
import { Role } from '../user/model/role.model';
import { RoleFeaturePermission } from '../user/model/role-feature-permission.model';
import { RoleSubFeaturePermission } from '../user/model/role-sub-feature-permission.model';
import { User } from '../user/model/user.model';
import { UserRole } from '../user/model/user-role.model';
import { Department } from '../user/model/department.model';
import { Roles } from '@apply-goal-backend/common';
import { UserDepartment } from '../user/model/user‐department.model';
import { Organization } from '../organization/organization.model';

// Load JSON from disk
const DATA: Array<{
  role: string;
  department: string;
  modules: Array<{
    module: string;
    features: Array<{
      feature: string;
      permissions: boolean;
      subFeatures?: Array<{ subFeature: string; permissions: boolean }>;
    }>;
  }>;
}> = require('./json/role-permission.json');

// Load organization data
const ORGANIZATION_DATA: {
  organizations: Array<{
    name: string;
    type: string;
    description?: string;
    website?: string;
    imageUrl?: string;
    address?: string;
    country?: string;
    state?: string;
    city?: string;
    postalCode?: string;
    phone?: string;
    email?: string;
    isActive: boolean;
  }>;
} = require('./json/organization.json');

@Injectable()
export class SeedService {
  private readonly logger = new Logger(SeedService.name);

  constructor(
    @InjectModel(Module) private moduleModel: typeof Module,
    @InjectModel(Feature) private featureModel: typeof Feature,
    @InjectModel(SubFeature) private subFeatureModel: typeof SubFeature,
    @InjectModel(Role) private roleModel: typeof Role,
    @InjectModel(RoleFeaturePermission)
    private roleFeaturePermissionModel: typeof RoleFeaturePermission,
    @InjectModel(RoleSubFeaturePermission)
    private roleSubFeaturePermissionModel: typeof RoleSubFeaturePermission,
    @InjectModel(User) private userModel: typeof User,
    @InjectModel(UserRole) private userRoleModel: typeof UserRole,
    @InjectModel(Department) private departmentModel: typeof Department,
    @InjectModel(UserDepartment)
    private userDeptModel: typeof UserDepartment,
    @InjectModel(Organization) private organizationModel: typeof Organization
  ) {}

  async seed() {
    this.logger.log('Starting database seed…');
    const tx = await this.moduleModel.sequelize.transaction();

    try {
      await this.seedOrganizations(tx);
      await this.seedDepartments(tx);
      await this.seedModules(tx);
      await this.seedFeatures(tx);
      await this.seedSubFeatures(tx);
      await this.seedRoles(tx);
      await this.seedRolePermissions(tx);
      await this.seedSuperAdmin(tx);

      await tx.commit();
      this.logger.log('🌱 Seed completed successfully');
    } catch (err) {
      await tx.rollback();
      this.logger.error('Seed failed:', err);
      throw err;
    }
  }

  private async seedOrganizations(tx) {
    const existing = await this.organizationModel.count({ transaction: tx });
    if (existing > 0) {
      this.logger.log(
        '▶️  Organizations already exist → skipping organization seed.'
      );
      return;
    }
    this.logger.log('🔷 Seeding Organizations…');

    // Create organizations from JSON data
    const organizationRows = ORGANIZATION_DATA.organizations.map((org) => ({
      name: org.name,
      type: org.type,
      description: org.description,
      website: org.website,
      imageUrl: org.imageUrl,
      address: org.address,
      country: org.country,
      state: org.state,
      city: org.city,
      postalCode: org.postalCode,
      phone: org.phone,
      email: org.email,
      isActive: org.isActive,
    }));

    await this.organizationModel.bulkCreate(organizationRows, {
      transaction: tx,
      ignoreDuplicates: true,
    });

    this.logger.log(`✅ Created ${organizationRows.length} organizations`);
  }

  private async seedDepartments(tx) {
    const existing = await this.departmentModel.count({ transaction: tx });
    if (existing > 0) {
      this.logger.log(
        '▶️  Departments already exist → skipping department seed.'
      );
      return;
    }
    this.logger.log('🔷 Seeding Departments…');

    // Normalize: department may be a string or an array of { name }
    const allDeptNames = DATA.flatMap((block) => {
      if (Array.isArray(block.department)) {
        return block.department.map((dep) => dep.name.trim());
      }
      if (typeof block.department === 'string') {
        return [block.department.trim()];
      }
      return [];
    });

    const uniqueNames = Array.from(new Set(allDeptNames));
    const deptRows = uniqueNames.map((name) => ({ name }));
    await this.departmentModel.bulkCreate(deptRows, {
      transaction: tx,
      ignoreDuplicates: true,
    });
  }

  private async seedModules(tx) {
    const existing = await this.moduleModel.count({ transaction: tx });
    if (existing > 0) {
      this.logger.log('▶️  Modules already exist → skipping module seed.');
      return;
    }
    this.logger.log('🔷 Seeding Modules…');

    // Extract unique module names from DATA
    const allModuleNames = Array.from(
      new Set(DATA.flatMap((r) => r.modules.map((m) => m.module)))
    );
    const moduleRows = allModuleNames.map((name) => ({ name }));
    await this.moduleModel.bulkCreate(moduleRows, {
      transaction: tx,
      ignoreDuplicates: true,
    });
  }

  private async seedFeatures(tx) {
    const existing = await this.featureModel.count({ transaction: tx });
    if (existing > 0) {
      this.logger.log('▶️  Features already exist → skipping feature seed.');
      return;
    }
    this.logger.log('🔷 Seeding Features…');

    // Build a map: moduleName → moduleId
    const modules = await this.moduleModel.findAll({ transaction: tx });
    const moduleMap = new Map<string, bigint>();
    modules.forEach((m) => {
      moduleMap.set(m.name, m.id);
    });

    // Collect all (moduleId, featureName) pairs
    const featureTuples: Array<{ moduleId: bigint; name: string }> = [];
    for (const block of DATA) {
      for (const mod of block.modules) {
        const mid = moduleMap.get(mod.module)!;
        for (const feat of mod.features) {
          featureTuples.push({ moduleId: mid, name: feat.feature });
        }
      }
    }

    // Deduplicate by `${moduleId}:${featureName}`
    const uniqueFeatures = Array.from(
      new Map(featureTuples.map((r) => [`${r.moduleId}:${r.name}`, r])).values()
    );

    await this.featureModel.bulkCreate(uniqueFeatures, {
      transaction: tx,
      ignoreDuplicates: true,
    });
  }

  private async seedSubFeatures(tx) {
    const existing = await this.subFeatureModel.count({ transaction: tx });
    if (existing > 0) {
      this.logger.log(
        '▶️  SubFeatures already exist → skipping sub-feature seed.'
      );
      return;
    }
    this.logger.log('🔷 Seeding SubFeatures…');

    // Build a map: featureName → featureId
    const features = await this.featureModel.findAll({ transaction: tx });
    const featureMap = new Map<string, bigint>();
    features.forEach((f) => {
      featureMap.set(f.name, f.id);
    });

    // Collect all (featureId, subFeatureName) pairs
    const subFeatureTuples: Array<{ featureId: bigint; name: string }> = [];
    for (const block of DATA) {
      for (const mod of block.modules) {
        for (const feat of mod.features) {
          if (!feat.subFeatures) continue;
          const fid = featureMap.get(feat.feature)!;
          for (const sf of feat.subFeatures) {
            subFeatureTuples.push({
              featureId: fid,
              name: sf.subFeature.trim(),
            });
          }
        }
      }
    }

    // Deduplicate by `${featureId}:${subFeatureName}`
    const uniqueSubFeatures = Array.from(
      new Map(
        subFeatureTuples.map((r) => [`${r.featureId}:${r.name}`, r])
      ).values()
    );

    await this.subFeatureModel.bulkCreate(uniqueSubFeatures, {
      transaction: tx,
      ignoreDuplicates: true,
    });
  }

  private async seedRoles(tx) {
    const existing = await this.roleModel.count({ transaction: tx });
    if (existing > 0) {
      this.logger.log('▶️  Roles already exist → skipping role seed.');
      return;
    }
    this.logger.log('🔷 Seeding Roles…');

    // Extract unique role names from DATA
    const roleNames = Array.from(new Set(DATA.map((r) => r.role)));
    const roleRows = roleNames.map((name) => ({ name }));
    await this.roleModel.bulkCreate(roleRows, { transaction: tx });
  }

  private async seedRolePermissions(tx) {
    const existing = await this.roleFeaturePermissionModel.count({
      transaction: tx,
    });
    if (existing > 0) {
      this.logger.log(
        '▶️  Role-Feature & Role-SubFeature permissions already exist → skipping.'
      );
      return;
    }
    this.logger.log('🔷 Seeding Role→Feature and Role→SubFeature permissions…');

    // 1) Load all Roles, Features, SubFeatures into maps for fast lookup
    const roles = await this.roleModel.findAll({ transaction: tx });
    const features = await this.featureModel.findAll({ transaction: tx });
    const subFeatures = await this.subFeatureModel.findAll({ transaction: tx });

    const roleMap = new Map<string, bigint>();
    roles.forEach((r) => {
      roleMap.set(r.name, r.id);
    });

    const featureMap = new Map<string, bigint>();
    features.forEach((f) => {
      featureMap.set(f.name, f.id);
    });

    const subFeatureMap = new Map<string, bigint>();
    subFeatures.forEach((sf) => {
      subFeatureMap.set(sf.name, sf.id);
    });

    // 2) Build two lists: one for RoleFeaturePermission, one for RoleSubFeaturePermission
    const rfpRows: Array<{
      roleId: bigint;
      featureId: bigint;
      isAllowed: boolean;
    }> = [];
    const rsfpRows: Array<{
      roleId: bigint;
      subFeatureId: bigint;
      isAllowed: boolean;
    }> = [];

    for (const block of DATA) {
      const rid = roleMap.get(block.role)!;

      for (const mod of block.modules) {
        for (const feat of mod.features) {
          const fid = featureMap.get(feat.feature)!;
          rfpRows.push({
            roleId: rid,
            featureId: fid,
            isAllowed: feat.permissions,
          });

          if (Array.isArray(feat.subFeatures)) {
            for (const sf of feat.subFeatures) {
              const sfName = sf.subFeature.trim();
              const sfid = subFeatureMap.get(sfName)!;
              rsfpRows.push({
                roleId: rid,
                subFeatureId: sfid,
                isAllowed: sf.permissions,
              });
            }
          }
        }
      }
    }

    // 3) Bulk insert
    await this.roleFeaturePermissionModel.bulkCreate(rfpRows, {
      transaction: tx,
      ignoreDuplicates: true,
    });
    await this.roleSubFeaturePermissionModel.bulkCreate(rsfpRows, {
      transaction: tx,
      ignoreDuplicates: true,
    });
  }

  private async seedSuperAdmin(tx) {
    // If a Super Admin user already exists, skip
    const existing = await this.userModel.findOne({
      where: { name: 'Super Admin' },
      transaction: tx,
    });
    if (existing) {
      this.logger.log(
        '▶️  Super Admin user already exists → skipping creation.'
      );
      return;
    }

    this.logger.log('🔷 Creating Super Admin user…');
    const hashed = await bcrypt.hash('superadmin123', 10);

    // 1) Find "Super Admin" role
    const superRole = await this.roleModel.findOne({
      where: { name: Roles.SUPER_ADMIN },
      transaction: tx,
    });
    if (!superRole) {
      throw new Error(
        `Unable to find role "${Roles.SUPER_ADMIN}" while seeding Super Admin.`
      );
    }

    // 2) Find "Super Admin" block in DATA
    const superBlock = DATA.find((b) => b.role.trim() === Roles.SUPER_ADMIN);
    if (!superBlock) {
      throw new Error(
        `Could not find "Super Admin" block in role‐permission JSON.`
      );
    }

    // 3) Normalize its department
    let deptName: string;
    if (Array.isArray(superBlock.department)) {
      if (superBlock.department.length === 0) {
        throw new Error('Super Admin block has empty department array');
      }
      deptName = superBlock.department[0].name.trim();
    } else if (typeof superBlock.department === 'string') {
      deptName = superBlock.department.trim();
    } else {
      throw new Error('Unexpected department format for Super Admin');
    }

    // 4) Lookup that department
    const deptRow = await this.departmentModel.findOne({
      where: { name: deptName },
      transaction: tx,
    });
    if (!deptRow) {
      throw new Error(
        `Department "${deptName}" not found while creating Super Admin.`
      );
    }

    // 5) Find ApplyGoal organization
    const applyGoalOrg = await this.organizationModel.findOne({
      where: { name: 'ApplyGoal' },
      transaction: tx,
    });

    // 6) Create the user with organization association
    const user = await this.userModel.create(
      {
        name: 'Super Admin',
        email: '<EMAIL>',
        password: hashed,
        status: 'active',
        organizationId: applyGoalOrg?.id || null,
      },
      { transaction: tx }
    );

    // 7) Assign role
    await this.userRoleModel.create(
      { userId: user.id, roleId: superRole.id },
      { transaction: tx }
    );

    // 8) Now link the department via the join table:
    await this.userDeptModel.create(
      { userId: user.id, departmentId: deptRow.id },
      { transaction: tx }
    );

    this.logger.log(
      `✅ Super Admin created and associated with ${
        applyGoalOrg?.name || 'no organization'
      }`
    );
  }
}
