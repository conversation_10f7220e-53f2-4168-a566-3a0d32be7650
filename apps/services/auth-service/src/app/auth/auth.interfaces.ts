export interface RegisterRequest {
  name: string;
  email: string;
  nationality: string;
  organizationName: string;
  password: string;
  phone: string;
  departmentName: string;
  roleName: string;
  ipAddress?: string;
  userAgent?: string;
}

export interface RegisterResponse {
  success: boolean;
  message: string;
}

export interface LoginRequest {
  email: string;
  password: string;
  ipAddress: string;
  userAgent?: string;
}

export interface LoginResponse {
  success: boolean;
  message: string;
  accessToken?: string;
  refreshToken?: string;
  user?: any;
}

export interface LogoutRequest {
  accessToken: string;
}

export interface LogoutResponse {
  success: boolean;
  message: string;
}

export interface ValidateTokenRequest {
  token: string;
}

export interface ValidateTokenResponse {
  valid: boolean;
  message: string;
  user?: any;
}

export interface RefreshTokenRequest {
  refreshToken?: string;
}

export interface RefreshTokenResponse {
  success?: boolean;
  message?: string;
  accessToken?: string;
  refreshToken?: string;
}

export interface CreateAuditLogRequest {
  userId: number;
  userRole: string;
  actions: string;
  serviceName: string;
  resourceType: string;
  resourceId: number;
  description: string;
  metadata?: { [key: string]: string };
  ipAddress?: string;
  userAgent?: string;
  source?: string;
}

export interface GenerateOtpRequest {
  email: string;
  type: string; // 'registration', 'login', 'password-reset', etc.
  ipAddress?: string;
  userAgent?: string;
}

export interface GenerateOtpResponse {
  success: boolean;
  message: string;
  expiresAt?: Date;
}

export interface VerifyOtpRequest {
  email: string;
  otp: string;
  type: string;
  ipAddress?: string;
  userAgent?: string;
}

export interface VerifyOtpResponse {
  success: boolean;
  message: string;
}

export interface SsoAuthRequest {
  provider: 'google'; // for now
  token: string;
  email: string;
  name: string;
  ipAddress?: string;
  userAgent?: string;
}

export interface SsoAuthResponse {
  success: boolean;
  message: string;
  accessToken?: string;
  refreshToken?: string;
  user?: any;
}
