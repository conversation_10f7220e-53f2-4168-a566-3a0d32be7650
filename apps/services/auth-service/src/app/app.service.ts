import { Injectable, OnModuleInit } from '@nestjs/common';
import { MetricsService } from '@apply-goal-backend/monitoring';

@Injectable()
export class AppService implements OnModuleInit {
  private requestCounter: any;
  private requestDuration: any;
  private authenticationCounter: any;
  private authorizationCounter: any;
  private tokenOperationsHistogram: any;
  private activeSessionsGauge: any;
  private loginAttemptsCounter: any;
  private auditLogRequestCounter: any;
  private auditLogRequestDuration: any;
  private organizationOperationsCounter: any;
  private organizationOperationsDuration: any;
  private metricsInitialized = false;

  constructor(private readonly metricsService: MetricsService) {}

  onModuleInit() {
    // Initialize metrics only once
    if (!this.metricsInitialized) {
      console.log('Initializing metrics for auth-service...');
      this.initializeMetrics();
      this.metricsInitialized = true;
    } else {
      console.log('Metrics already initialized, skipping initialization...');
    }
  }

  private initializeMetrics() {
    try {
      // Check if metrics are already initialized
      if (this.requestCounter) {
        console.log('Metrics already initialized, skipping...');
        return;
      }

      // Initialize metrics with all required labels
      this.requestCounter = this.metricsService.createCounter(
        'http_requests_total',
        'Total HTTP requests',
        ['method', 'endpoint', 'status']
      );

      this.requestDuration = this.metricsService.createHistogram(
        'http_request_duration_seconds',
        'HTTP request duration',
        ['method', 'endpoint']
      );

      this.authenticationCounter = this.metricsService.createCounter(
        'authentication_attempts_total',
        'Total authentication attempts',
        ['result', 'reason']
      );

      this.authorizationCounter = this.metricsService.createCounter(
        'authorization_attempts_total',
        'Total authorization attempts',
        ['type', 'resource', 'result']
      );

      this.tokenOperationsHistogram = this.metricsService.createHistogram(
        'token_operation_duration_seconds',
        'Token operation duration',
        ['operation']
      );

      this.activeSessionsGauge = this.metricsService.createGauge(
        'active_sessions',
        'Number of active sessions',
        ['type']
      );

      this.loginAttemptsCounter = this.metricsService.createCounter(
        'login_attempts_total',
        'Total login attempts',
        ['result', 'reason']
      );

      this.auditLogRequestCounter = this.metricsService.createCounter(
        'audit_log_requests_total',
        'Total audit log requests',
        ['operation', 'status']
      );

      this.auditLogRequestDuration = this.metricsService.createHistogram(
        'audit_log_request_duration_seconds',
        'Audit log request duration',
        ['operation']
      );

      this.organizationOperationsCounter = this.metricsService.createCounter(
        'organization_operations_total',
        'Total organization operations',
        ['operation', 'status']
      );

      this.organizationOperationsDuration = this.metricsService.createHistogram(
        'organization_operation_duration_seconds',
        'Organization operation duration',
        ['operation']
      );

      console.log('Metrics initialized successfully');
    } catch (error) {
      console.warn(
        'Error initializing metrics, they may already be registered:',
        error.message
      );

      // Set the flag to true even if initialization failed to prevent retries
      this.metricsInitialized = true;
    }
  }

  // Track metrics methods
  trackLoginAttempt(result: string, reason: string) {
    try {
      if (this.loginAttemptsCounter) {
        this.loginAttemptsCounter.inc({ result, reason });
      }
    } catch (error) {
      console.warn('Error tracking login attempt metric:', error.message);
    }
  }

  incrementActiveSessions(type: string) {
    try {
      if (this.activeSessionsGauge) {
        this.activeSessionsGauge.inc({ type });
      }
    } catch (error) {
      console.warn('Error incrementing active sessions metric:', error.message);
    }
  }

  decrementActiveSessions(type: string) {
    try {
      if (this.activeSessionsGauge) {
        this.activeSessionsGauge.dec({ type });
      }
    } catch (error) {
      console.warn('Error decrementing active sessions metric:', error.message);
    }
  }

  trackTokenOperation(operation: string, duration: number) {
    try {
      if (this.tokenOperationsHistogram) {
        this.tokenOperationsHistogram.observe({ operation }, duration);
      }
    } catch (error) {
      console.warn('Error tracking token operation metric:', error.message);
    }
  }

  trackAuthorization(type: string, resource: string, result: string) {
    try {
      if (this.authorizationCounter) {
        this.authorizationCounter.inc({ type, resource, result });
      }
    } catch (error) {
      console.warn('Error tracking authorization metric:', error.message);
    }
  }

  trackAuditLogRequest(operation: string, status: string, duration: number) {
    try {
      if (this.auditLogRequestCounter && this.auditLogRequestDuration) {
        this.auditLogRequestCounter.inc({ operation, status });
        this.auditLogRequestDuration.observe({ operation }, duration);
      }
    } catch (error) {
      console.warn('Error tracking audit log request metric:', error.message);
    }
  }

  trackHttpRequest(
    method: string,
    endpoint: string,
    status: string,
    duration: number
  ) {
    try {
      if (this.requestCounter && this.requestDuration) {
        this.requestCounter.inc({ method, endpoint, status });
        this.requestDuration.observe({ method, endpoint }, duration);
      }
    } catch (error) {
      console.warn('Error tracking HTTP request metric:', error.message);
    }
  }

  trackOrganizationOperation(
    operation: string,
    status: string,
    duration: number
  ) {
    try {
      if (
        this.organizationOperationsCounter &&
        this.organizationOperationsDuration
      ) {
        this.organizationOperationsCounter.inc({ operation, status });
        this.organizationOperationsDuration.observe({ operation }, duration);
      }
    } catch (error) {
      console.warn(
        'Error tracking organization operation metric:',
        error.message
      );
    }
  }

  getData() {
    return { message: 'Hello API' };
  }
}
