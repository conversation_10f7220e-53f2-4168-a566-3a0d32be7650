import { Injectable, OnModuleInit } from '@nestjs/common';
import { MetricsService } from '@apply-goal-backend/monitoring';

@Injectable()
export class AppService implements OnModuleInit {
  private requestCounter: any;
  private requestDuration: any;
  private authenticationCounter: any;
  private authorizationCounter: any;
  private tokenOperationsHistogram: any;
  private activeSessionsGauge: any;
  private loginAttemptsCounter: any;
  private auditLogRequestCounter: any;
  private auditLogRequestDuration: any;
  private metricsInitialized = false;
  
  constructor(private readonly metricsService: MetricsService) {}
  
  onModuleInit() {
    // Initialize metrics only once
    if (!this.metricsInitialized) {
      this.initializeMetrics();
      this.metricsInitialized = true;
    }
  }
  
  private initializeMetrics() {
    try {
      // Initialize metrics with all required labels
      this.requestCounter = this.metricsService.createCounter(
        'http_requests_total', 
        'Total HTTP requests',
        ['method', 'endpoint', 'status']
      );
      
      this.requestDuration = this.metricsService.createHistogram(
        'http_request_duration_seconds', 
        'HTTP request duration',
        ['method', 'endpoint']
      );
      
      this.authenticationCounter = this.metricsService.createCounter(
        'authentication_attempts_total', 
        'Total authentication attempts',
        ['result', 'reason']
      );
      
      this.authorizationCounter = this.metricsService.createCounter(
        'authorization_attempts_total', 
        'Total authorization attempts',
        ['type', 'resource', 'result']
      );
      
      this.tokenOperationsHistogram = this.metricsService.createHistogram(
        'token_operation_duration_seconds', 
        'Token operation duration',
        ['operation']
      );
      
      this.activeSessionsGauge = this.metricsService.createGauge(
        'active_sessions', 
        'Number of active sessions',
        ['type']
      );
      
      this.loginAttemptsCounter = this.metricsService.createCounter(
        'login_attempts_total', 
        'Total login attempts',
        ['result', 'reason']
      );
      
      this.auditLogRequestCounter = this.metricsService.createCounter(
        'audit_log_requests_total', 
        'Total audit log requests',
        ['operation', 'status']
      );
      
      this.auditLogRequestDuration = this.metricsService.createHistogram(
        'audit_log_request_duration_seconds', 
        'Audit log request duration',
        ['operation']
      );
    } catch (error) {
      console.warn('Error initializing metrics, they may already be registered:', error.message);
    }
  }
  
  // Track metrics methods
  trackLoginAttempt(result: string, reason: string) {
    if (this.loginAttemptsCounter) {
      this.loginAttemptsCounter.inc({ result, reason });
    }
  }
  
  incrementActiveSessions(type: string) {
    if (this.activeSessionsGauge) {
      this.activeSessionsGauge.inc({ type });
    }
  }
  
  decrementActiveSessions(type: string) {
    if (this.activeSessionsGauge) {
      this.activeSessionsGauge.dec({ type });
    }
  }
  
  trackTokenOperation(operation: string, duration: number) {
    if (this.tokenOperationsHistogram) {
      this.tokenOperationsHistogram.observe({ operation }, duration);
    }
  }
  
  trackAuthorization(type: string, resource: string, result: string) {
    if (this.authorizationCounter) {
      this.authorizationCounter.inc({ type, resource, result });
    }
  }
  
  trackAuditLogRequest(operation: string, status: string, duration: number) {
    if (this.auditLogRequestCounter && this.auditLogRequestDuration) {
      this.auditLogRequestCounter.inc({ operation, status });
      this.auditLogRequestDuration.observe({ operation }, duration);
    }
  }
  
  trackHttpRequest(method: string, endpoint: string, status: string, duration: number) {
    this.requestCounter.inc({ method, endpoint, status });
    this.requestDuration.observe({ method, endpoint }, duration);
  }
  
  getData() {
    return { message: 'Hello API' };
  }
}
