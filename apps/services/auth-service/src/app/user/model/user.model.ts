import {
  Column,
  Model,
  Table,
  DataType,
  HasMany,
  BelongsToMany,
  ForeignKey,
  BelongsTo,
  PrimaryKey,
  AutoIncrement,
} from 'sequelize-typescript';
import { Role } from './role.model';
import { UserRole } from './user-role.model';
import { SocialSite } from './social-site.model';
import { BaseModel } from '@apply-goal-backend/database';
import { Department } from './department.model';
import { UserDepartment } from './user‐department.model';

@Table({
  tableName: 'users',
  timestamps: true,
})
export class User extends BaseModel {
  @Column({
    type: DataType.STRING,
    allowNull: false,
    field: 'name',
  })
  name: string;

  @Column({
    type: DataType.STRING,
    allowNull: false,
    unique: true,
  })
  email: string;

  @Column({
    type: DataType.STRING,
  })
  password: string;

  @Column(DataType.STRING)
  phone: string;

  @Column(DataType.STRING)
  nationality: string;

  @Column(DataType.STRING(255))
  ppUrl?: string;

  @Column({
    type: DataType.ENUM('active', 'inactive', 'pending', 'suspended'),
    field: 'status',
    defaultValue: 'inactive',
  })
  status: string;

  // ── User ↔ Department (M:N)
  @BelongsToMany(() => Department, {
    through: () => UserDepartment,
    foreignKey: 'userId',
    otherKey: 'departmentId',
    as: 'departments',
  })
  departments: Department[];
  @HasMany(() => SocialSite)
  socialSites: SocialSite[];

  @BelongsToMany(
    () => Role,
    () => UserRole,
    'userId', // FK in user_roles pointing at User
    'roleId'
  )
  roles: Role[];
}
