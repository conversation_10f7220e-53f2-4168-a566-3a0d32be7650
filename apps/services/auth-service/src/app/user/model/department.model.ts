// department.model.ts
import {
  Table,
  Column,
  DataType,
  ForeignKey,
  Model,
  BelongsTo,
  HasMany,
  BelongsToMany,
} from 'sequelize-typescript';
import { BaseModel } from '@apply-goal-backend/database';
import { User } from './user.model';
import { Feature } from './feature.model';
import { SubFeature } from './sub-feature.model';
import { DepartmentFeaturePermission } from './department-feature-permission.model';
import { DepartmentSubFeaturePermission } from './department-sub-feature-permission.model';
import { UserDepartment } from './user‐department.model';
import { Organization } from '../../organization/organization.model';

@Table({
  tableName: 'departments',
  timestamps: true,
})
export class Department extends BaseModel {
  @Column({ type: DataType.STRING, allowNull: false })
  name: string;

  @ForeignKey(() => Department)
  @Column({ type: DataType.BIGINT, allowNull: true, field: 'parent_id' })
  parentId?: number;

  @BelongsTo(() => Department, 'parentId')
  parent?: Department;

  @HasMany(() => Department, 'parentId')
  children?: Department[];

  @BelongsToMany(() => User, () => UserDepartment, 'departmentId', 'userId')
  users?: User[];

  @ForeignKey(() => Organization)
  @Column({ type: DataType.BIGINT, allowNull: false, field: 'organization_id' })
  organizationId: bigint;

  // ———— M:N to Feature via DepartmentFeaturePermission ————
  @BelongsToMany(
    () => Feature,
    () => DepartmentFeaturePermission,
    'departmentId', // FK on the join table
    'featureId' // otherKey on the join table
  )
  features?: Array<
    Feature & { DepartmentFeaturePermission: DepartmentFeaturePermission }
  >;

  @BelongsToMany(
    () => SubFeature,
    () => DepartmentSubFeaturePermission,
    'departmentId', // FK on the join table
    'subFeatureId' // otherKey on the join table
  )
  subFeatures?: Array<
    SubFeature & {
      DepartmentSubFeaturePermission: DepartmentSubFeaturePermission;
    }
  >;
}
