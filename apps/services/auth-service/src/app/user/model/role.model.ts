import { Column, Table, DataType, BelongsToMany } from 'sequelize-typescript';
import { BaseModel } from '@apply-goal-backend/database';
import { Feature } from './feature.model';
import { RoleFeaturePermission } from './role-feature-permission.model';
import { RoleSubFeaturePermission } from './role-sub-feature-permission.model';
import { SubFeature } from './sub-feature.model';
import { UserRole } from './user-role.model';
import { User } from './user.model';

@Table({
  tableName: 'roles',
  timestamps: true,
})
export class Role extends BaseModel {
  @Column({
    type: DataType.STRING,
    allowNull: false,
  })
  name: string;

  // === Permission includes scope ===
  static readonly permissionIncludes = [
    {
      model: Feature,
      as: 'features',
      through: { model: RoleFeaturePermission, where: { isAllowed: true } },
      include: [{ model: require('./module.model').Module, as: 'module' }],
    },
    {
      model: SubFeature,
      as: 'subFeatures',
      through: { model: RoleSubFeaturePermission, where: { isAllowed: true } },
      include: [{ model: Feature, as: 'feature' }],
    },
  ];

  static initScopes() {
    this.addScope('withPermissions', {
      include: Role.permissionIncludes,
    });
  }

  @BelongsToMany(() => Feature, {
    through: () => RoleFeaturePermission,
    as: 'features',
    foreignKey: 'roleId',
    otherKey: 'featureId',
  })
  features: Array<Feature & { RoleFeaturePermission: RoleFeaturePermission }>;

  @BelongsToMany(() => SubFeature, {
    through: () => RoleSubFeaturePermission,
    as: 'subFeatures',
    foreignKey: 'roleId',
    otherKey: 'subFeatureId',
  })
  subFeatures: Array<
    SubFeature & { RoleSubFeaturePermission: RoleSubFeaturePermission }
  >;

  @BelongsToMany(
    () => User,
    () => UserRole,
    'roleId', // FK in user_roles pointing at Role
    'userId' // other FK pointing at User
  )
  users: User[];
}
