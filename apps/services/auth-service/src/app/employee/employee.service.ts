import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';
import { EmployeePersonal } from './models/employee-personal.model';
import { CreateEmployeeDto } from './dto/create-employee.dto';
import { UpdateEmployeeDto } from './dto/update-employee.dto';

@Injectable()
export class EmployeeService {
  constructor(
    @InjectModel(EmployeePersonal)
    private readonly employeeModel: typeof EmployeePersonal
  ) {}

  async create(data: CreateEmployeeDto): Promise<EmployeePersonal> {
    return this.employeeModel.create(data as any);
  }

  async findAll(): Promise<EmployeePersonal[]> {
    return this.employeeModel.findAll({
      include: { all: true, nested: true },
    });
  }

  async findOne(id: number): Promise<EmployeePersonal> {
    const emp = await this.employeeModel.findByPk(id, {
      include: { all: true, nested: true },
    });
    if (!emp) throw new NotFoundException(`Employee ${id} not found`);
    return emp;
  }

  async update(id: number, data: UpdateEmployeeDto): Promise<EmployeePersonal> {
    const emp = await this.findOne(id);
    await emp.update(data as any);
    return emp;
  }

  async remove(id: number): Promise<{ deleted: boolean }> {
    const emp = await this.findOne(id);
    await emp.destroy();
    return { deleted: true };
  }
}
