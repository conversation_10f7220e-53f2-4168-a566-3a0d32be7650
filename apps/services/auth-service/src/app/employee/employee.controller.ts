import { Controller } from '@nestjs/common';
import { GrpcMethod } from '@nestjs/microservices';
import { EmployeeService } from './employee.service';
import {
  CreateEmployeeRequest,
  EmployeeResponse,
  GetEmployeeRequest,
  ListEmployeesRequest,
  RemoveEmployeeRequest,
  UpdateEmployeeRequest,
} from './employee.interface';

@Controller()
export class EmployeeController {
  constructor(private readonly svc: EmployeeService) {}

  @GrpcMethod('AuthService', 'CreateEmployee')
  async createEmployee(req: CreateEmployeeRequest): Promise<EmployeeResponse> {
    const emp = await this.svc.create(req);
    return { employee: emp.toJSON() };
  }

  @GrpcMethod('AuthService', 'GetEmployee')
  async getEmployee(req: GetEmployeeRequest): Promise<EmployeeResponse> {
    const emp = await this.svc.findOne(req.id);
    return { employee: emp.toJSON() };
  }

  @GrpcMethod('AuthService', 'ListEmployees')
  async listEmployees(_: ListEmployeesRequest): Promise<{ employees: any[] }> {
    const all = await this.svc.findAll();
    return { employees: all.map((e) => e.toJSON()) };
  }

  @GrpcMethod('AuthService', 'UpdateEmployee')
  async updateEmployee(req: UpdateEmployeeRequest): Promise<EmployeeResponse> {
    const emp = await this.svc.update(req.id, req);
    return { employee: emp.toJSON() };
  }

  @GrpcMethod('AuthService', 'RemoveEmployee')
  async removeEmployee(
    _: RemoveEmployeeRequest
  ): Promise<{ success: boolean }> {
    await this.svc.remove(_.id);
    return { success: true };
  }
}
