import { Controller, Logger } from '@nestjs/common';
import { GrpcMethod } from '@nestjs/microservices';
import { EmployeeService } from './employee.service';
import {
  CreateEmployeeRequest,
  EmployeeResponse,
  GetEmployeeRequest,
  ListEmployeesRequest,
  ListEmployeesResponse,
  RemoveEmployeeRequest,
  RemoveEmployeeResponse,
  UpdateEmployeeRequest,
} from './employee.interface';

@Controller()
export class EmployeeController {
  private readonly logger = new Logger(EmployeeController.name);

  constructor(private readonly svc: EmployeeService) {}

  @GrpcMethod('AuthService', 'CreateEmployee')
  async createEmployee(req: CreateEmployeeRequest): Promise<EmployeeResponse> {
    try {
      this.logger.log(`gRPC CreateEmployee request for user: ${req.userId}`);
      return await this.svc.createEmployee(req);
    } catch (error) {
      this.logger.error(`CreateEmployee error: ${error.message}`, error.stack);
      return {
        success: false,
        message: error.message || 'Failed to create employee',
      };
    }
  }

  @GrpcMethod('AuthService', 'GetEmployee')
  async getEmployee(req: GetEmployeeRequest): Promise<EmployeeResponse> {
    try {
      this.logger.log(`gRPC GetEmployee request: ${req.id}`);
      // For now, use legacy method - will be enhanced later
      const emp = await this.svc.findOne(Number(req.id));
      return {
        success: true,
        message: 'Employee retrieved successfully',
        employee: await this.svc.mapToEmployeeInfo(emp),
      };
    } catch (error) {
      this.logger.error(`GetEmployee error: ${error.message}`, error.stack);
      return {
        success: false,
        message: error.message || 'Failed to get employee',
      };
    }
  }

  @GrpcMethod('AuthService', 'ListEmployees')
  async listEmployees(
    req: ListEmployeesRequest
  ): Promise<ListEmployeesResponse> {
    try {
      this.logger.log('gRPC ListEmployees request');
      // For now, use legacy method - will be enhanced later
      const all = await this.svc.findAll();
      return {
        success: true,
        message: 'Employees retrieved successfully',
        employees: await Promise.all(
          all.map((e) => this.svc.mapToEmployeeInfo(e))
        ),
        total: all.length,
        page: req.page || 1,
        limit: req.limit || 10,
      };
    } catch (error) {
      this.logger.error(`ListEmployees error: ${error.message}`, error.stack);
      return {
        success: false,
        message: error.message || 'Failed to list employees',
        employees: [],
        total: 0,
        page: req.page || 1,
        limit: req.limit || 10,
      };
    }
  }

  @GrpcMethod('AuthService', 'UpdateEmployee')
  async updateEmployee(req: UpdateEmployeeRequest): Promise<EmployeeResponse> {
    try {
      this.logger.log(`gRPC UpdateEmployee request: ${req.id}`);
      // For now, use legacy method - will be enhanced later
      const emp = await this.svc.update(Number(req.id), req as any);
      return {
        success: true,
        message: 'Employee updated successfully',
        employee: await this.svc.mapToEmployeeInfo(emp),
      };
    } catch (error) {
      this.logger.error(`UpdateEmployee error: ${error.message}`, error.stack);
      return {
        success: false,
        message: error.message || 'Failed to update employee',
      };
    }
  }

  @GrpcMethod('AuthService', 'RemoveEmployee')
  async removeEmployee(
    req: RemoveEmployeeRequest
  ): Promise<RemoveEmployeeResponse> {
    try {
      this.logger.log(`gRPC RemoveEmployee request: ${req.id}`);
      // For now, use legacy method - will be enhanced later
      await this.svc.remove(Number(req.id));
      return {
        success: true,
        message: 'Employee removed successfully',
      };
    } catch (error) {
      this.logger.error(`RemoveEmployee error: ${error.message}`, error.stack);
      return {
        success: false,
        message: error.message || 'Failed to remove employee',
      };
    }
  }
}
