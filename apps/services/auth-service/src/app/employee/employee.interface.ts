export interface CreateEmployeeRequest {
  userId: number;
  lastName?: string;
  firstName?: string;
  joiningDate?: Date;
  jobType?: string;
  jobStatus?: string;
  dateOfBirth?: Date;
  bloodGroup?: string;
  gender?: string;
  nationality?: string;
  phoneNumber?: string;
  email?: string;
}

export interface UpdateEmployeeRequest {
  id: number;
  lastName?: string;
  firstName?: string;
  joiningDate?: Date;
  jobType?: string;
  jobStatus?: string;
  dateOfBirth?: Date;
  bloodGroup?: string;
  gender?: string;
  nationality?: string;
  phoneNumber?: string;
  email?: string;
}

export interface GetEmployeeRequest {
  id: number;
}

export interface ListEmployeesRequest {
  page: number;
  limit: number;
  search?: string;
}

export interface RemoveEmployeeRequest {
  id: number;
}

export interface EmployeeResponse {
  employee: any;
}
