import { BaseModel } from '@apply-goal-backend/database';
import {
  Table,
  Column,
  Model,
  DataType,
  ForeignKey,
  BelongsTo,
  Default,
  Index,
  PrimaryKey,
  AutoIncrement,
} from 'sequelize-typescript';

@Table({
  tableName: 'organization',
  timestamps: true,
  paranoid: true, // soft deletes
})
export class Organization extends BaseModel {
  @Column(DataType.STRING)
  name: string;

  @Column(DataType.STRING)
  type: string;

  @Column(DataType.STRING)
  imageUrl: string;
}
