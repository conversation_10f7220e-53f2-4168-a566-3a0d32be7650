import { BaseModel } from '@apply-goal-backend/database';
import {
  Table,
  Column,
  Model,
  DataType,
  ForeignKey,
  BelongsTo,
  Default,
  Index,
  PrimaryKey,
  AutoIncrement,
  HasMany,
  AllowNull,
  Length,
  IsEmail,
  IsUrl,
} from 'sequelize-typescript';
import { User } from '../user/model/user.model';
import { Department } from '../user/model/department.model';

@Table({
  tableName: 'organizations',
  timestamps: true,
  paranoid: true, // soft deletes
  indexes: [
    {
      unique: true,
      fields: ['name'],
      where: {
        deletedAt: null,
      },
    },
    {
      fields: ['type'],
    },
    {
      fields: ['country'],
    },
  ],
})
export class Organization extends BaseModel {
  @AllowNull(false)
  @Length({ min: 2, max: 255 })
  @Column({
    type: DataType.STRING(255),
    allowNull: false,
  })
  name: string;

  @AllowNull(false)
  @Column({
    type: DataType.ENUM('university', 'agency', 'company'),
    allowNull: false,
    defaultValue: 'other',
  })
  type: string;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
  })
  description: string;

  @IsUrl
  @Column({
    type: DataType.STRING(500),
    allowNull: true,
  })
  website: string;

  @IsUrl
  @Column({
    type: DataType.STRING(500),
    allowNull: true,
  })
  imageUrl: string;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
  })
  address: string;

  @Column({
    type: DataType.STRING(100),
    allowNull: true,
  })
  country: string;

  @Column({
    type: DataType.STRING(100),
    allowNull: true,
  })
  state: string;

  @Column({
    type: DataType.STRING(100),
    allowNull: true,
  })
  city: string;

  @Column({
    type: DataType.STRING(20),
    allowNull: true,
  })
  postalCode: string;

  @Column({
    type: DataType.STRING(20),
    allowNull: true,
  })
  phone: string;

  @IsEmail
  @Column({
    type: DataType.STRING(255),
    allowNull: true,
  })
  email: string;

  // Relationships
  @HasMany(() => User, {
    foreignKey: 'organizationId',
    as: 'users',
  })
  users?: User[];

  @HasMany(() => Department, {
    foreignKey: 'organizationId',
    as: 'departments',
  })
  departments?: Department[];
}
