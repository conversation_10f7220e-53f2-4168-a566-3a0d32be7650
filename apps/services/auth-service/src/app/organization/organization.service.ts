import {
  Injectable,
  Logger,
  NotFoundException,
  ConflictException,
  BadRequestException,
} from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';
import { Op, Transaction, UniqueConstraintError } from 'sequelize';
import { firstValueFrom } from 'rxjs';
import { Organization } from './organization.model';
import { AuditClientService } from '../audit/audit.service';
import {
  CreateOrganizationRequest,
  CreateOrganizationResponse,
  GetOrganizationRequest,
  GetOrganizationResponse,
  UpdateOrganizationRequest,
  UpdateOrganizationResponse,
  DeleteOrganizationRequest,
  DeleteOrganizationResponse,
  ListOrganizationsRequest,
  ListOrganizationsResponse,
  OrganizationInfo,
  CreateOrganizationData,
  UpdateOrganizationData,
  ORGANIZATION_TYPES,
} from './organization.interface';
import { CreateAuditLogRequest } from '../auth/auth.interfaces';

@Injectable()
export class OrganizationService {
  private readonly logger = new Logger(OrganizationService.name);

  constructor(
    @InjectModel(Organization)
    private readonly organizationModel: typeof Organization,
    private readonly auditService: AuditClientService
  ) {}

  async createOrganization(
    request: CreateOrganizationRequest
  ): Promise<CreateOrganizationResponse> {
    try {
      this.logger.log(`Creating organization: ${request.name}`);

      // Validate organization type
      if (!ORGANIZATION_TYPES.includes(request.type as any)) {
        throw new BadRequestException(
          `Invalid organization type. Must be one of: ${ORGANIZATION_TYPES.join(
            ', '
          )}`
        );
      }

      // Prepare organization data
      const organizationData: CreateOrganizationData = {
        name: request.name,
        type: request.type,
        imageUrl: request.imageUrl,
        description: request.description,
        website: request.website,
        address: request.address,
        country: request.country,
        state: request.state,
        city: request.city,
        postalCode: request.postalCode,
        phone: request.phone,
        email: request.email,
      };

      // Create organization
      const organization = await this.organizationModel.create(
        organizationData as any
      );

      // Log audit
      await this.logAudit({
        userId: Number(request.userId),
        userRole: request.roleName,
        actions: 'CREATE_ORGANIZATION',
        serviceName: 'auth-service',
        resourceType: 'Organization',
        resourceId: Number(organization.id),
        description: `Created organization: ${organization.name}`,
        ipAddress: request.ipAddress,
        userAgent: request.userAgent,
        source: 'grpc',
      });

      this.logger.log(`Organization created successfully: ${organization.id}`);

      return {
        success: true,
        message: 'Organization created successfully',
        organization: this.mapToOrganizationInfo(organization),
      };
    } catch (error) {
      this.logger.error(
        `Error creating organization: ${error.message}`,
        error.stack
      );

      if (error instanceof UniqueConstraintError) {
        throw new ConflictException(
          'Organization with this name already exists'
        );
      }

      if (error instanceof BadRequestException) {
        throw error;
      }

      throw new Error('Failed to create organization');
    }
  }

  async getOrganization(
    request: GetOrganizationRequest
  ): Promise<GetOrganizationResponse> {
    try {
      this.logger.log(`Getting organization: ${request.id}`);

      const organization = await this.organizationModel.findByPk(request.id);

      if (!organization) {
        throw new NotFoundException('Organization not found');
      }

      // Log audit
      await this.logAudit({
        userId: Number(request.userId),
        userRole: request.roleName,
        actions: 'VIEW_ORGANIZATION',
        serviceName: 'auth-service',
        resourceType: 'Organization',
        resourceId: Number(organization.id),
        description: `Viewed organization: ${organization.name}`,
        ipAddress: request.ipAddress,
        userAgent: request.userAgent,
        source: 'grpc',
      });

      return {
        success: true,
        message: 'Organization retrieved successfully',
        organization: this.mapToOrganizationInfo(organization),
      };
    } catch (error) {
      this.logger.error(
        `Error getting organization: ${error.message}`,
        error.stack
      );

      if (error instanceof NotFoundException) {
        throw error;
      }

      throw new Error('Failed to get organization');
    }
  }

  async updateOrganization(
    request: UpdateOrganizationRequest
  ): Promise<UpdateOrganizationResponse> {
    try {
      this.logger.log(`Updating organization: ${request.id}`);

      const organization = await this.organizationModel.findByPk(request.id);

      if (!organization) {
        throw new NotFoundException('Organization not found');
      }

      // Validate organization type if provided
      if (request.type && !ORGANIZATION_TYPES.includes(request.type as any)) {
        throw new BadRequestException(
          `Invalid organization type. Must be one of: ${ORGANIZATION_TYPES.join(
            ', '
          )}`
        );
      }

      // Prepare update data
      const updateData: UpdateOrganizationData = {};
      if (request.name !== undefined) updateData.name = request.name;
      if (request.type !== undefined) updateData.type = request.type;
      if (request.imageUrl !== undefined)
        updateData.imageUrl = request.imageUrl;
      if (request.description !== undefined)
        updateData.description = request.description;
      if (request.website !== undefined) updateData.website = request.website;
      if (request.address !== undefined) updateData.address = request.address;
      if (request.country !== undefined) updateData.country = request.country;
      if (request.state !== undefined) updateData.state = request.state;
      if (request.city !== undefined) updateData.city = request.city;
      if (request.postalCode !== undefined)
        updateData.postalCode = request.postalCode;
      if (request.phone !== undefined) updateData.phone = request.phone;
      if (request.email !== undefined) updateData.email = request.email;

      // Update organization
      await organization.update(updateData);

      // Log audit
      await this.logAudit({
        userId: Number(request.userId),
        userRole: request.roleName,
        actions: 'UPDATE_ORGANIZATION',
        serviceName: 'auth-service',
        resourceType: 'Organization',
        resourceId: Number(organization.id),
        description: `Updated organization: ${organization.name}`,
        ipAddress: request.ipAddress,
        userAgent: request.userAgent,
        source: 'grpc',
      });

      this.logger.log(`Organization updated successfully: ${organization.id}`);

      return {
        success: true,
        message: 'Organization updated successfully',
        organization: this.mapToOrganizationInfo(organization),
      };
    } catch (error) {
      this.logger.error(
        `Error updating organization: ${error.message}`,
        error.stack
      );

      if (
        error instanceof NotFoundException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }

      if (error instanceof UniqueConstraintError) {
        throw new ConflictException(
          'Organization with this name already exists'
        );
      }

      throw new Error('Failed to update organization');
    }
  }

  async deleteOrganization(
    request: DeleteOrganizationRequest
  ): Promise<DeleteOrganizationResponse> {
    try {
      this.logger.log(`Deleting organization: ${request.id}`);

      const organization = await this.organizationModel.findByPk(request.id);

      if (!organization) {
        throw new NotFoundException('Organization not found');
      }

      // Soft delete the organization
      await organization.destroy();

      // Log audit
      await this.logAudit({
        userId: Number(request.userId),
        userRole: request.roleName,
        actions: 'DELETE_ORGANIZATION',
        serviceName: 'auth-service',
        resourceType: 'Organization',
        resourceId: Number(organization.id),
        description: `Deleted organization: ${organization.name}`,
        ipAddress: request.ipAddress,
        userAgent: request.userAgent,
        source: 'grpc',
      });

      this.logger.log(`Organization deleted successfully: ${organization.id}`);

      return {
        success: true,
        message: 'Organization deleted successfully',
      };
    } catch (error) {
      this.logger.error(
        `Error deleting organization: ${error.message}`,
        error.stack
      );

      if (error instanceof NotFoundException) {
        throw error;
      }

      throw new Error('Failed to delete organization');
    }
  }

  async listOrganizations(
    request: ListOrganizationsRequest
  ): Promise<ListOrganizationsResponse> {
    try {
      this.logger.log('Listing organizations');

      const page = request.page || 1;
      const limit = request.limit || 10;
      const offset = (page - 1) * limit;

      // Build where conditions
      const whereConditions: any = {};

      if (request.search) {
        whereConditions[Op.or] = [
          { name: { [Op.iLike]: `%${request.search}%` } },
          { description: { [Op.iLike]: `%${request.search}%` } },
          { email: { [Op.iLike]: `%${request.search}%` } },
        ];
      }

      if (request.type) {
        whereConditions.type = request.type;
      }

      // Get organizations with pagination
      const { rows: organizations, count: total } =
        await this.organizationModel.findAndCountAll({
          where: whereConditions,
          limit,
          offset,
          order: [['createdAt', 'DESC']],
        });

      // Log audit
      await this.logAudit({
        userId: Number(request.userId),
        userRole: request.roleName,
        actions: 'LIST_ORGANIZATIONS',
        serviceName: 'auth-service',
        resourceType: 'Organization',
        resourceId: 0,
        description: `Listed organizations (page: ${page}, limit: ${limit})`,
        ipAddress: request.ipAddress,
        userAgent: request.userAgent,
        source: 'grpc',
      });

      return {
        success: true,
        message: 'Organizations retrieved successfully',
        organizations: organizations.map((org) =>
          this.mapToOrganizationInfo(org)
        ),
        total,
        page,
        limit,
      };
    } catch (error) {
      this.logger.error(
        `Error listing organizations: ${error.message}`,
        error.stack
      );
      throw new Error('Failed to list organizations');
    }
  }

  // Helper method to map Organization model to OrganizationInfo
  private mapToOrganizationInfo(organization: Organization): OrganizationInfo {
    return {
      id: organization.id,
      name: organization.name,
      type: organization.type,
      imageUrl: organization.imageUrl,
      description: organization.description,
      website: organization.website,
      address: organization.address,
      country: organization.country,
      state: organization.state,
      city: organization.city,
      postalCode: organization.postalCode,
      phone: organization.phone,
      email: organization.email,
      isActive: organization.isActive,
      createdAt: organization.createdAt,
      updatedAt: organization.updatedAt,
    };
  }

  // Helper method to log audit events
  private async logAudit(auditData: CreateAuditLogRequest): Promise<void> {
    try {
      await firstValueFrom(this.auditService.createAuditLog(auditData));
    } catch (error) {
      this.logger.warn(`Failed to log audit: ${error.message}`);
      // Don't throw error for audit logging failures
    }
  }

  // Additional helper methods for business logic
  async findByName(name: string): Promise<Organization | null> {
    return this.organizationModel.findOne({
      where: { name },
    });
  }

  async findByType(type: string): Promise<Organization[]> {
    return this.organizationModel.findAll({
      where: { type },
      order: [['name', 'ASC']],
    });
  }

  async getOrganizationStats(): Promise<{
    total: number;
    byType: Record<string, number>;
    active: number;
    inactive: number;
  }> {
    const total = await this.organizationModel.count();
    const active = await this.organizationModel.count({
      where: { isActive: true },
    });
    const inactive = total - active;

    // Get count by type
    const typeStats = await this.organizationModel.findAll({
      attributes: [
        'type',
        [
          this.organizationModel.sequelize!.fn(
            'COUNT',
            this.organizationModel.sequelize!.col('id')
          ),
          'count',
        ],
      ],
      group: ['type'],
      raw: true,
    });

    const byType: Record<string, number> = {};
    typeStats.forEach((stat: any) => {
      byType[stat.type] = parseInt(stat.count, 10);
    });

    return {
      total,
      byType,
      active,
      inactive,
    };
  }
}
