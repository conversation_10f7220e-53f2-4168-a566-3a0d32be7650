import { Modu<PERSON> } from '@nestjs/common';
import { SequelizeModule } from '@nestjs/sequelize';
import { Organization } from './organization.model';
import { OrganizationController } from './organization.controller';
import { OrganizationService } from './organization.service';
import { AuditModule } from '../audit/audit.module';
import { DatabaseModule } from '../migration/database.module';

@Module({
  imports: [
    DatabaseModule,
    SequelizeModule.forFeature([Organization]),
    AuditModule,
  ],
  controllers: [OrganizationController],
  providers: [OrganizationService],
  exports: [SequelizeModule, OrganizationService],
})
export class OrganizationModule {}
