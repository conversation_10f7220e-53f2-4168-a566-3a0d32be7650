# ▶️ Running ApplyGoal Services with Nx Only (No Docker)

This guide shows how to run your Nx-based microservices locally **without Dock<PERSON>** — assuming all infrastructure is installed and running on your Windows host.

---

## ✅ 1. Prerequisites

- PostgreSQL (port 5432)
- Redis (port 6379)
- RabbitMQ (port 5672)
- MinIO server (port 9000)
- Node.js v18+
- Yarn or PNPM (depending on your workspace)
- Nx CLI: `npm install -g nx`

---

## 📁 2. Setup `.env` at the Monorepo Root

Create a file called `.env` with the following:

```dotenv
DB_HOST=localhost
DB_PORT=5432
DB_USER=postgres
DB_PASSWORD=postgres123

REDIS_HOST=localhost
REDIS_PORT=6379

RABBITMQ_URI=amqp://rabbitmq_user:rabbitmq_pass@localhost:5672

S3_ENDPOINT=http://localhost:9000
S3_PUBLIC_ENDPOINT=http://localhost:9000
S3_BUCKET=applygoal-files
S3_ACCESS_KEY=minioadmin
S3_SECRET_KEY=minioadmin123
S3_REGION=us-east-1
S3_FORCE_PATH_STYLE=true
```

---

## 🧠 3. Initialize Dependencies (Once)

```bash
pnpm install    # or yarn install
nx run-many --target=build --all
```

---

## 🚀 4. Start Individual Services

```bash
nx serve auth-apigw
nx serve auth-service
nx serve payment-service
```

---

## 🛠 5. Start Multiple Services in Parallel

```bash
nx run-many --target=serve --projects=auth-apigw,auth-service,payment-service --parallel
```

---

## 🧪 6. Verify Health

Once services are up:

- Auth API: http://localhost:4006/health
- Auth Service: http://localhost:5003/health

---

## 🌐 7. Access Services

If using Traefik on your host, configure routes like:

```toml
[http.routers.auth-apigw]
  rule = "Host(`auth-api.localhost`)"
  service = "auth-apigw"

[http.services.auth-apigw.loadBalancer]
  [[http.services.auth-apigw.loadBalancer.servers]]
    url = "http://localhost:4006"
```

---

## 🧩 8. Uploads via MinIO

Ensure MinIO is running on port `9000` and bucket `applygoal-files` is publicly readable.

Uploaded files will be accessible via:

```
http://localhost:9000/applygoal-files/images/<file>
```

---

## ✅ You're Now Running Nx-Only Microservices Without Docker!