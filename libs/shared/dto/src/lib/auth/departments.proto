syntax = "proto3";
package auth;
import "common.proto";

message CreateDepartmentRequest {
  repeated NewDepartmentInfo departments = 1;
  int64 userId = 3;
  string roleName = 4;
  string ipAddress = 5;
  string userAgent = 6;
}
message NewDepartmentInfo {
  string name = 1;
  string parent = 2;
}
message CreateDepartmentResponse {
  bool success = 1;
  string message = 2;
  DepartmentInfo department = 3;
}

message AssignDepartmentRequest {
  int64 userId = 1;
  int64 departmentId = 2;
}
message AssignDepartmentResponse {
  bool success = 1;
  string message = 2;
}

message ListDepartmentsResponse {
  bool success = 1;
  string message = 2;
  repeated DepartmentInfo departments = 3;
}
message ListDepartmentsWithUsersResponse {
  bool success = 1;
  string message = 2;
  repeated DepartmentWithUsersInfo departments = 3;
}

message DepartmentWithUsersInfo {
  int64 id = 1;
  string name = 2;
  optional int64 parent_id = 3;
  repeated DepartmentUserInfo users = 4;
  repeated DepartmentWithUsersInfo children = 5;
}
message DepartmentUserInfo {
  int64 id = 1;
  string name = 2;
  string email = 3;
}
