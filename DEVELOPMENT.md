# ApplyGoal Backend Development Guide

This guide provides detailed information for developers working on the ApplyGoal Backend project.

## Development Environment

### Recommended Tools

- **IDE**: Visual Studio Code with the following extensions:
  - Nx Console
  - ESLint
  - Prettier
  - NestJS Snippets
- **Node.js**: v18 or later
- **npm**: v8 or later

### Environment Setup

1. Install Node.js and npm
2. Clone the repository
3. Install dependencies:
   ```sh
   npm install
   ```
4. Install the Nx CLI globally (optional but recommended):
   ```sh
   npm install -g nx
   ```

## Project Structure

The project follows a monorepo structure using Nx:

- `apps/`: Contains all application services
  - `apply-goal-backend/`: Main application
  - `core/`: Core services
  - `gateways/`: API gateways
  - `services/`: Domain services
- `libs/`: Contains shared libraries
  - `shared/`: Shared code across services
- `scripts/`: Helper scripts
- `*-e2e/`: End-to-end test projects

## Development Workflow

### Starting Development

1. Decide which service(s) you need to work on
2. Start the required services:
   ```sh
   npm run services service1 service2 service3
   ```
3. Make your changes
4. Run tests to verify your changes
5. Submit a pull request

### Code Style and Linting

The project uses ESLint and Prettier for code style and linting:

```sh
# Run linting on all projects
npx nx run-many --target=lint --all

# Run linting on a specific project
npx nx lint project-name

# Format code
npx nx format:write
```

### Testing

The project uses Jest for testing:

```sh
# Run tests for all projects
npx nx run-many --target=test --all

# Run tests for a specific project
npx nx test project-name

# Run tests with coverage
npx nx test project-name --coverage
```

### Debugging

1. Start the service in debug mode:
   ```sh
   npx nx serve service-name --inspect
   ```
2. Attach your debugger to the Node.js process

## Working with Services

### Service Communication

Services communicate with each other using HTTP/REST APIs. In the future, we may implement message queues for asynchronous communication.

### Adding a New Service

1. Generate a new NestJS application:
   ```sh
   npx nx g @nx/nest:app new-service --directory=services
   ```
2. Configure the service port in `scripts/service-ports.js`
3. Add npm scripts to run the service in `package.json`
4. Update documentation in `SERVICES.md`

### Shared Code

Place shared code in the `libs/shared` directory:

```sh
# Create a new shared library
npx nx g @nx/node:lib new-lib --directory=shared
```

## Database Access

Currently, the project does not have database configuration. When implementing database access:

1. Use TypeORM or Mongoose based on the database type
2. Create entity models in the appropriate service
3. Configure database connection in the service's module

## API Documentation

When implementing APIs, document them using Swagger:

1. Add Swagger annotations to controllers and DTOs
2. Configure Swagger in the service's main.ts file:
   ```typescript
   import { SwaggerModule, DocumentBuilder } from '@nestjs/swagger';

   // In the bootstrap function
   const config = new DocumentBuilder()
     .setTitle('Service Name API')
     .setDescription('API description')
     .setVersion('1.0')
     .build();
   const document = SwaggerModule.createDocument(app, config);
   SwaggerModule.setup('api/docs', app, document);
   ```

## Deployment

### Building for Production

```sh
# Build all services
npx nx run-many --target=build --all --prod

# Build a specific service
npx nx build service-name --prod
```

### Docker

In the future, we will add Docker support for containerization:

1. Create a Dockerfile for each service
2. Create a docker-compose.yml file for local development
3. Configure CI/CD to build and push Docker images

## Troubleshooting

### Common Issues

1. **Port conflicts**: Ensure no other applications are using the assigned ports
2. **Dependency issues**: Run `npm install` to ensure all dependencies are installed
3. **Build errors**: Check for TypeScript errors and fix them

### Getting Help

If you encounter issues:

1. Check the existing documentation
2. Review the error messages
3. Search for similar issues in the project repository
4. Ask for help from other team members
