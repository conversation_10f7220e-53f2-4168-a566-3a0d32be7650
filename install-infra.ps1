# Windows PowerShell Script to Install and Start Infrastructure Tools (Redis, PostgreSQL, RabbitMQ, MinIO)

# Ensure Chocolatey is installed
if (!(Get-Command choco.exe -ErrorAction SilentlyContinue)) {
    Set-ExecutionPolicy Bypass -Scope Process -Force
    [System.Net.ServicePointManager]::SecurityProtocol = [System.Net.ServicePointManager]::SecurityProtocol -bor 3072
    iex ((New-Object System.Net.WebClient).DownloadString('https://community.chocolatey.org/install.ps1'))
}

# Install Redis
choco install redis-64 -y
Start-Service redis

# Install PostgreSQL
choco install postgresql -y
Start-Service postgresql

# Install Erlang and RabbitMQ
choco install erlang -y
choco install rabbitmq -y
Start-Service rabbitmq

# Install MinIO Client
choco install minio-client -y --ignore-checksums

# Start MinIO Server (if minio.exe is available)
$minioPath = "$env:ProgramData\chocolatey\bin\minio.exe"
if (Test-Path $minioPath) {
    Start-Process -FilePath $minioPath -ArgumentList "server", "C:\minio-data" -NoNewWindow
    Write-Host "✅ MinIO server started at http://localhost:9000"
} else {
    Write-Warning "MinIO server executable not found. Please ensure MinIO server is installed and run manually if needed."
}

Write-Host "`n✅ Infrastructure components installed and started. Some services may require manual configuration."
