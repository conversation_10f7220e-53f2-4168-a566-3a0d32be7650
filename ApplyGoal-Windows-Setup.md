# 🚀 ApplyGoal Local Deployment Guide (Windows)

This guide walks you through setting up and running the ApplyGoal microservices environment on a **Windows system**, where infrastructure services are installed natively.

---

## 📦 1. Install Infrastructure (Redis, PostgreSQL, RabbitMQ, MinIO)

Run the PowerShell script:

```powershell
./install-infra.ps1
```

This installs:
- Redis
- PostgreSQL
- RabbitMQ (+ Erlang)
- MinIO CLI (`mc`)

---

## ⚙️ 2. Configure MinIO

After starting your MinIO server locally (on port 9000):

Run:

```powershell
./configure-infra.ps1
```

This will:
- Create the `applygoal-files` bucket (if not exists)
- Set public read-only access for all uploaded files

You can then access uploaded files via:
```
http://localhost:9000/applygoal-files/images/<filename>
```

---

## 🗄️ 3. Initialize PostgreSQL Databases and Users

Ensure PostgreSQL is running and `pg_isready` / `psql` are accessible.

Then run the script:

```bash
./init-postgres.sh
```

It will create and grant access to all required databases and users, like:

- `audit_db` → `audit_user`
- `students_db` → `students_user`
- ... and more.

---

## 🧪 4. Configure `.env`

Use the provided `.env` template to configure each service:

```
DB_HOST=localhost
REDIS_HOST=localhost
RABBITMQ_URI=amqp://rabbitmq_user:rabbitmq_pass@localhost:5672
S3_ENDPOINT=http://localhost:9000
S3_PUBLIC_ENDPOINT=http://localhost:9000
S3_BUCKET=applygoal-files
...
```

Place `.env` at the project root or inject via Nx CLI as needed.

---

## 🛠️ 5. Start Nx Services

Build and serve services using Nx CLI:

```bash
nx serve auth-apigw
nx serve auth-service
```

Or run multiple:

```bash
nx run-many --target=serve --projects=auth-apigw,auth-service,identity-service --parallel
```

---

## 🌐 6. Configure Host-Traefik (Optional)

If Traefik is running on the host:

Edit your Traefik config to proxy to your local services:

```toml
[http.routers.auth-apigw]
  rule = "Host(`auth-api.localhost`)"
  service = "auth-apigw"

[http.services.auth-apigw.loadBalancer]
  [[http.services.auth-apigw.loadBalancer.servers]]
    url = "http://localhost:4006"
```

Then restart Traefik.

---

## 🐳 7. Use Docker Compose (App Services Only)

Run only the app containers using:

```bash
docker compose -f docker-compose.windows.yml up --build
```

This assumes Redis, Postgres, RabbitMQ, MinIO, and Traefik are running **outside Docker**.

---

## ✅ You’re Ready!

- Upload files via `/upload`
- View images in MinIO: `http://localhost:9000/applygoal-files/images/...`
- Monitor APIs via Traefik: `http://traefik.localhost`

---