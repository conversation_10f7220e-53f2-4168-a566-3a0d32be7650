<#
.SYNOPSIS
    Uninstall Infrastructure Tools (Redis, PostgreSQL, RabbitMQ, MinIO Client) installed via Chocolatey.

.DESCRIPTION
    This script stops related services, uninstalls packages via Chocolatey, and removes data folders.

.NOTES
    Run this script in an elevated PowerShell (Admin) session).
#>

# Stop and uninstall Redis
Write-Host "Stopping and uninstalling Redis..."
if (Get-Service -Name redis -ErrorAction SilentlyContinue) {
    Stop-Service redis -Force -ErrorAction SilentlyContinue
    sc.exe delete redis | Out-Null
}
choco uninstall redis-64 -y

# Stop and uninstall PostgreSQL
Write-Host "Stopping and uninstalling PostgreSQL..."
if (Get-Service -Name postgresql -ErrorAction SilentlyContinue) {
    Stop-Service postgresql -Force -ErrorAction SilentlyContinue
    sc.exe delete postgresql | Out-Null
}
choco uninstall postgresql -y

# Stop and uninstall RabbitMQ & Erlang
Write-Host "Stopping and uninstalling RabbitMQ..."
if (Get-Service -Name RabbitMQ -ErrorAction SilentlyContinue) {
    Stop-Service RabbitMQ -Force -ErrorAction SilentlyContinue
    sc.exe delete RabbitMQ | Out-Null
}
Write-Host "Stopping and uninstalling Erlang..."
if (Get-Service -Name "Erlang*" -ErrorAction SilentlyContinue) {
    Get-Service "Erlang*" | Stop-Service -Force -ErrorAction SilentlyContinue
    Get-WmiObject win32_service | Where-Object { $_.Name -like 'Erlang*' } | ForEach-Object { sc.exe delete $_.Name | Out-Null }
}
choco uninstall rabbitmq -y
choco uninstall erlang -y

# Uninstall MinIO client
Write-Host "Uninstalling MinIO Client..."
choco uninstall minio-client -y

# Remove data directories (adjust paths if needed)
Write-Host "Removing data directories..."
$paths = @(
    'C:\ProgramData\chocolatey\lib\redis-64', 
    'C:\ProgramData\chocolatey\lib\postgresql', 
    'C:\ProgramData\chocolatey\lib\rabbitmq', 
    'C:\ProgramData\chocolatey\lib\erlang', 
    'C:\ProgramData\chocolatey\lib\minio-client',
    'C:\minio-data'  # MinIO server data dir
)
foreach ($p in $paths) {
    if (Test-Path $p) {
        Remove-Item $p -Recurse -Force -ErrorAction SilentlyContinue
        Write-Host "Removed $p"
    }
}

Write-Host "`n✅ Uninstallation of infrastructure components complete."